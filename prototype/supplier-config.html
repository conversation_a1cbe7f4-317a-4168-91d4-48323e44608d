<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商配置管理 - 采购协同配置中心</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="app">
        <!-- 主要内容区域 -->
        <main class="main">
            <div class="main__container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb__list">
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">🏠 首页</a>
                        </li>
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">配置管理</a>
                        </li>
                        <li class="breadcrumb__item breadcrumb__item--current">
                            <span class="breadcrumb__text">供应商配置</span>
                        </li>
                    </ol>
                </nav>

                <!-- 页面标题 -->
                <section class="page-header">
                    <h2 class="page-header__title">🏢 供应商配置管理</h2>
                    <p class="page-header__description">
                        管理供应商级别的配置参数，支持继承业务方配置或自定义配置。配置优先级：供应商级别 > 业务方级别。
                    </p>
                    <div class="page-header__meta">
                        <span class="page-header__level">📊 配置级别: Level 3 - 供应商级别</span>
                    </div>
                </section>

                <!-- 搜索筛选区域 -->
                <section class="search" id="searchContainer">
                    <div class="search__basic">
                        <div class="search__field">
                            <label class="search__label">🔍 关键词:</label>
                            <input type="text" name="keyword" class="input search__input" placeholder="供应商代码、供应商名称、业务方名称">
                        </div>
                        <div class="search__field">
                            <label class="search__label">🏭 业务方:</label>
                            <select name="factoryCode" class="input search__input">
                                <option value="">全部业务方</option>
                                <option value="F001">1502 - 上海业务方</option>
                                <option value="F002">1502 - 北京业务方</option>
                                <option value="F003">15A2 - 广州业务方</option>
                            </select>
                        </div>
                        <div class="search__field">
                            <button class="btn btn--primary search__btn">🔍 搜索</button>
                        </div>
                    </div>
                    
                    <div class="search__advanced" style="display: none;">
                        <div class="search__advanced-grid">
                            <div class="search__field">
                                <label class="search__label">🏢 供应商代码:</label>
                                <input type="text" name="supplierCode" class="input search__input" placeholder="S001">
                            </div>
                            <div class="search__field">
                                <label class="search__label">🏢 供应商名称:</label>
                                <input type="text" name="supplierName" class="input search__input" placeholder="风机叶片供应商A">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 创建时间:</label>
                                <input type="date" name="createTimeStart" class="input search__input">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 至:</label>
                                <input type="date" name="createTimeEnd" class="input search__input">
                            </div>
                        </div>
                        <div class="search__actions">
                            <button class="btn btn--primary">🔍 高级搜索</button>
                            <button class="btn btn--secondary">📋 保存条件</button>
                            <button class="btn btn--secondary">📂 加载预设</button>
                        </div>
                    </div>
                </section>

                <!-- 工具栏 -->
                <section class="toolbar">
                    <div class="toolbar__left">
                        <button class="btn btn--primary" id="addSupplierBtn">➕ 新增供应商</button>
                        <button class="btn btn--secondary" id="batchConfigBtn" disabled>🔧 批量配置</button>
                    </div>
                    <div class="toolbar__right">
                        <button class="btn btn--secondary" id="exportBtn">📥 导入</button>
                        <button class="btn btn--secondary" id="exportBtn">📤 导出</button>
                    </div>
                </section>

                <!-- 数据表格 -->
                <section class="table-section">
                    <div id="tableContainer" class="table-container">
                        <!-- 表格内容将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 移动端卡片视图 -->
                    <div id="mobileCards" class="mobile-cards d-none">
                        <!-- 移动端卡片将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 分页 -->
                <section id="paginationContainer" class="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer__container">
                <p class="footer__text">
                    © 2024 上海电气风电集团有限公司 - 采购协同配置中心 v1.0
                </p>
            </div>
        </footer>
    </div>

    <!-- 新增/编辑供应商模态框 -->
    <div id="supplierModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 600px;">
            <div class="modal__header">
                <h3 class="modal__title" id="modalTitle">新增供应商</h3>
                <button class="modal__close" onclick="closeSupplierModal()">×</button>
            </div>
            <div class="modal__body">
                <form id="supplierForm">
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="form-field">
                            <label class="form-label">🏭 所属业务方 *</label>
                            <select name="factoryCode" class="input" required>
                                <option value="">请选择业务方</option>
                                <option value="F001">1502 - 上海业务方</option>
                                <option value="F002">1503 - 北京业务方</option>
                                <option value="F003">F003 - 广州业务方</option>
                                <option value="F004">F004 - 天津业务方</option>
                            </select>
                        </div>
                        <div class="form-field">
                            <label class="form-label">🏢 供应商代码 *</label>
                            <input type="text" name="supplierCode" class="input" required placeholder="S001">
                        </div>
                    </div>
                    <div class="form-field" style="margin-bottom: 16px;">
                        <label class="form-label">🏢 供应商名称 *</label>
                        <input type="text" name="supplierName" class="input" required placeholder="风机叶片供应商A">
                    </div>
                    <div class="form-actions" style="text-align: right; border-top: 1px solid #f0f0f0; padding-top: 16px;">
                        <button type="button" class="btn btn--secondary" onclick="closeSupplierModal()" style="margin-right: 8px;">取消</button>
                        <button type="submit" class="btn btn--primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 配置参数模态框 -->
    <div id="configModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 800px;">
            <div class="modal__header">
                <h3 class="modal__title" id="configModalTitle">供应商配置</h3>
                <button class="modal__close" onclick="closeConfigModal()">×</button>
            </div>
            <div class="modal__body">
                <div id="configContent">
                    <!-- 配置内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 继承关系视图模态框 -->
    <div id="inheritanceModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 900px;">
            <div class="modal__header">
                <h3 class="modal__title">🔗 配置继承关系视图</h3>
                <button class="modal__close" onclick="closeInheritanceModal()">×</button>
            </div>
            <div class="modal__body">
                <div id="inheritanceContent">
                    <!-- 继承关系内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 (移动端) -->
    <button class="fab d-none" id="mobileFab">+</button>

    <script src="js/common.js"></script>
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSupplierConfigPage();
        });

        // 全局变量
        let currentData = [];
        let currentFilters = {};
        let currentPage = 1;
        let currentPageSize = CONFIG.DEFAULT_PAGE_SIZE;
        let tableComponent = null;
        let searchComponent = null;
        let paginationComponent = null;
        let accordionComponent = null;

        // 初始化页面
        function initSupplierConfigPage() {
            initComponents();
            loadData();
            bindEvents();
            setupResponsive();
        }

        // 初始化组件
        function initComponents() {
            // 搜索组件
            searchComponent = new SearchComponent(document.getElementById('searchContainer'), {
                onSearch: handleSearch,
                onReset: handleReset
            });

            // 分页组件
            paginationComponent = new PaginationComponent(document.getElementById('paginationContainer'), {
                onChange: handlePageChange,
                onShowSizeChange: handlePageSizeChange
            });
        }

        // 加载数据
        function loadData() {
            const filters = {
                ...currentFilters,
                page: currentPage,
                pageSize: currentPageSize
            };

            const result = DataManager.getSuppliers(filters);
            currentData = result.data;

            renderTable();
            paginationComponent.update(result.total, result.page);
            updateToolbarState();
        }

        // 渲染表格
        function renderTable() {
            const columns = [
                {
                    title: '供应商代码',
                    dataIndex: 'supplierCode',
                    render: (value, record) => `<strong>${value}</strong>`
                },
                {
                    title: '供应商名称',
                    dataIndex: 'supplierName'
                },
                {
                    title: '所属业务方',
                    dataIndex: 'factoryName',
                    render: (value, record) => `${record.factoryCode} - ${value}`
                },
                {
                    title: '更新时间',
                    dataIndex: 'updateTime',
                    className: 'table__cell--secondary'
                },
                {
                    title: '操作',
                    dataIndex: 'actions',
                    render: (value, record) => `
                        <div class="table__actions">
                            <button class="btn btn--small btn--primary" onclick="editSupplier('${record.id}')">编辑</button>
                            <button class="btn btn--small btn--secondary" onclick="configSupplier('${record.id}')">配置</button>
                            <button class="btn btn--small btn--danger" onclick="deleteSupplier('${record.id}')">删除</button>
                        </div>
                    `
                }
            ];

            // 检查是否为移动端
            if (window.innerWidth < 768) {
                renderMobileCards();
            } else {
                renderDesktopTable(columns);
            }
        }

        // 渲染桌面端表格
        function renderDesktopTable(columns) {
            document.getElementById('mobileCards').classList.add('d-none');
            document.getElementById('tableContainer').classList.remove('d-none');

            tableComponent = new TableComponent(document.getElementById('tableContainer'), {
                columns: columns,
                data: currentData,
                selectable: true,
                expandable: true,
                expandedRowRender: renderConfigParams,
                onSelectionChange: handleSelectionChange,
                onExpand: handleRowExpand
            });

            // 初始化手风琴组件
            setTimeout(() => {
                const accordionContainer = document.querySelector('.table-container');
                if (accordionContainer) {
                    accordionComponent = new AccordionComponent(accordionContainer);
                }
            }, 100);
        }

        // 渲染移动端卡片
        function renderMobileCards() {
            document.getElementById('tableContainer').classList.add('d-none');
            const mobileContainer = document.getElementById('mobileCards');
            mobileContainer.classList.remove('d-none');

            mobileContainer.innerHTML = currentData.map(record => {
                let statusIcon = '';
                switch (record.status) {
                    case 'CONFIGURED': statusIcon = '🔧'; break;
                    case 'INHERITED': statusIcon = '🔗'; break;
                    default: statusIcon = '⚠️';
                }

                return `
                    <div class="mobile-card" data-id="${record.id}">
                        <div class="mobile-card__header">
                            <div class="mobile-card__title">${record.supplierCode} - ${record.supplierName}</div>
                            <div class="mobile-card__actions">
                                <button class="btn btn--small btn--primary" onclick="editSupplier('${record.id}')">编辑</button>
                                <button class="btn btn--small btn--secondary" onclick="configSupplier('${record.id}')">配置</button>
                            </div>
                        </div>
                        <div class="mobile-card__content">
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">所属业务方</div>
                                <div class="mobile-card__value">${record.factoryCode} - ${record.factoryName}</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">配置状态</div>
                                <div class="mobile-card__value">
                                    <span class="status ${record.status === 'CONFIGURED' ? 'status--success' : record.status === 'INHERITED' ? 'status--info' : 'status--warning'}">
                                        ${statusIcon} ${record.configStatus}
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">继承链</div>
                                <div class="mobile-card__value">${record.inheritanceChain ? record.inheritanceChain.join(' → ') : '-'}</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">更新时间</div>
                                <div class="mobile-card__value">${record.updateTime}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // 显示浮动操作按钮
            document.getElementById('mobileFab').classList.remove('d-none');
        }

        // 渲染配置参数
        function renderConfigParams(record) {
            if (!record.config && record.status !== 'INHERITED') {
                return `
                    <div class="config-params">
                        <div class="config-params__empty">
                            <p>🔧 该供应商尚未配置参数</p>
                            <button class="btn btn--primary" onclick="configSupplier('${record.id}')">立即配置</button>
                            <button class="btn btn--secondary" onclick="inheritFromFactory('${record.id}')">继承业务方配置</button>
                        </div>
                    </div>
                `;
            }

            // 获取业务方配置作为继承基础
            const factoryConfig = FACTORY_DATA.find(f => f.factoryCode === record.factoryCode)?.config || {};
            
            const params = Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                let value, source, displayValue;
                
                if (record.config && record.config[param.key] !== null && record.config[param.key] !== undefined) {
                    // 自定义配置
                    value = record.config[param.key];
                    source = 'CUSTOM';
                } else {
                    // 继承配置
                    value = factoryConfig[param.key];
                    source = 'INHERITED';
                }
                
                if (param.type === 'boolean') {
                    displayValue = value ? '✅ 开启' : '❌ 关闭';
                } else if (param.type === 'select') {
                    const option = param.options.find(opt => opt.value === value);
                    displayValue = option ? option.label : value;
                } else {
                    displayValue = value || '';
                }

                const sourceIcon = source === 'CUSTOM' ? '🔧' : '🔗';
                const sourceText = source === 'CUSTOM' ? '自定义' : '继承';
                const sourceClass = source === 'CUSTOM' ? 'config-source--custom' : 'config-source--inherited';

                return `
                    <div class="config-param">
                        <div class="config-param__header">
                            <span class="config-param__icon">${param.icon}</span>
                            <span class="config-param__label">${param.label}</span>
                            <span class="config-source ${sourceClass}">${sourceIcon} ${sourceText}</span>
                        </div>
                        <div class="config-param__value">${displayValue}</div>
                        <div class="config-param__actions">
                            <button class="btn btn--small btn--secondary" onclick="editParam('${record.id}', '${param.key}')">
                                编辑
                            </button>
                            ${source === 'CUSTOM' ? `
                                <button class="btn btn--small btn--tertiary" onclick="resetToInherit('${record.id}', '${param.key}')">
                                    重置继承
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            return `
                <div class="config-params">
                    <div class="config-params__header">
                        <h4>📋 配置参数</h4>
                        <div class="config-params__actions">
                            <button class="btn btn--small btn--primary" onclick="configSupplier('${record.id}')">编辑配置</button>
                            <button class="btn btn--small btn--secondary" onclick="copyConfig('${record.id}')">复制配置</button>
                            <button class="btn btn--small btn--info" onclick="viewInheritance('${record.id}')">查看继承</button>
                        </div>
                    </div>
                    <div class="config-params__grid">
                        ${params}
                    </div>
                </div>
            `;
        }

        // 事件绑定
        function bindEvents() {
            // 新增供应商
            document.getElementById('addSupplierBtn').addEventListener('click', () => {
                showSupplierModal();
            });

            // 批量操作
            document.getElementById('batchConfigBtn').addEventListener('click', () => {
                showBatchConfigModal();
            });
            document.getElementById('batchDeleteBtn').addEventListener('click', () => {
                handleBatchDelete();
            });

            // 展开/收起
            document.getElementById('expandAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.expandAll();
                }
            });

            document.getElementById('collapseAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.collapseAll();
                }
            });

            // 继承视图
            document.getElementById('inheritanceViewBtn').addEventListener('click', () => {
                showInheritanceView();
            });

            // 其他操作
            document.getElementById('exportBtn').addEventListener('click', handleExport);
            document.getElementById('refreshBtn').addEventListener('click', loadData);

            // 移动端浮动按钮
            document.getElementById('mobileFab').addEventListener('click', () => {
                showSupplierModal();
            });

            // 供应商表单提交
            document.getElementById('supplierForm').addEventListener('submit', handleSupplierSubmit);
        }

        // 搜索处理
        function handleSearch(filters) {
            currentFilters = filters;
            currentPage = 1;
            loadData();
        }

        // 重置处理
        function handleReset() {
            currentFilters = {};
            currentPage = 1;
            loadData();
        }

        // 分页处理
        function handlePageChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 页面大小改变处理
        function handlePageSizeChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 选择改变处理
        function handleSelectionChange(selectedRows) {
            updateToolbarState(selectedRows.length > 0);
        }

        // 行展开处理
        function handleRowExpand(record, expanded) {
            console.log('Row expand:', record.supplierCode, expanded);
        }

        // 更新工具栏状态
        function updateToolbarState(hasSelection = false) {
            document.getElementById('batchConfigBtn').disabled = !hasSelection;
            document.getElementById('inheritConfigBtn').disabled = !hasSelection;
            document.getElementById('batchDeleteBtn').disabled = !hasSelection;
        }

        // 响应式设置
        function setupResponsive() {
            window.addEventListener('resize', Utils.debounce(() => {
                renderTable();
            }, 300));
        }

        // 供应商操作函数
        function editSupplier(id) {
            const supplier = currentData.find(item => item.id === id);
            if (supplier) {
                showSupplierModal(supplier);
            }
        }

        function configSupplier(id) {
            const supplier = currentData.find(item => item.id === id);
            if (supplier) {
                showConfigModal(supplier);
            }
        }

        function viewInheritance(id) {
            const supplier = currentData.find(item => item.id === id);
            if (supplier) {
                showInheritanceModal(supplier);
            }
        }

        function deleteSupplier(id) {
            const supplier = currentData.find(item => item.id === id);
            if (supplier) {
                Utils.confirm(`确定要删除供应商 "${supplier.supplierName}" 吗？`, '删除确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已删除供应商: ${supplier.supplierName}`, 'success');
                        loadData();
                    }
                });
            }
        }

        function inheritFromFactory(id) {
            const supplier = currentData.find(item => item.id === id);
            if (supplier) {
                Utils.confirm(`确定要让供应商 "${supplier.supplierName}" 继承业务方配置吗？`, '继承确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已设置为继承业务方配置`, 'success');
                        loadData();
                    }
                });
            }
        }

        // 模态框操作
        function showSupplierModal(supplier = null) {
            const modal = document.getElementById('supplierModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('supplierForm');
            
            if (supplier) {
                title.textContent = '编辑供应商';
                form.factoryCode.value = supplier.factoryCode;
                form.supplierCode.value = supplier.supplierCode;
                form.supplierName.value = supplier.supplierName;
                form.supplierCode.readOnly = true;
            } else {
                title.textContent = '新增供应商';
                form.reset();
                form.supplierCode.readOnly = false;
            }
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeSupplierModal() {
            const modal = document.getElementById('supplierModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 解析参数配置
        function resolveParameterConfig(supplier, paramKey, chain) {
            let value, source, sourceLevel, displayValue;
             if (chain.factory && chain.factory.config && chain.factory.config[paramKey] !== null && chain.factory.config[paramKey] !== undefined) {
                // 业务方级别配置（最低优先级）
                value = chain.factory.config[paramKey];
                source = 'FACTORY';
                sourceLevel = '业务方';
            } else {
                // 完全未配置
                value = null;
                source = 'NONE';
                sourceLevel = '未配置';
            }

            // 格式化显示值
            const param = Object.values(CONFIG.CONFIG_PARAMS).find(p => p.key === paramKey);
            if (param) {
                if (value === null || value === undefined) {
                    displayValue = '-';
                } else if (param.type === 'boolean') {
                    displayValue = value ? '✅ 开启' : '❌ 关闭';
                } else if (param.type === 'select') {
                    const option = param.options.find(opt => opt.value === value);
                    displayValue = option ? option.label : value;
                } else {
                    displayValue = value;
                }
            } else {
                displayValue = value || '-';
            }

            return { value, source, sourceLevel, displayValue };
        }

        // 获取继承链
        function getInheritanceChain(supplier) {
            const chain = {
                factory: null,
                supplier: supplier,
                category: null
            };
            // 获取业务方配置
            if (supplier.factoryCode) {
                chain.factory = FACTORY_DATA.find(f => f.factoryCode === supplier.factoryCode);
            }

            return chain;
        }

         // 渲染参数继承链
        function renderParameterChain(paramKey, chain) {
            const levels = [
                { key: 'factory', label: '业务方', icon: '🏭', data: chain.factory },
            ];

            return levels.map(level => {
                if (!level.data) return '';

                const config = level.data.config;
                const hasValue = config && config[paramKey] !== null && config[paramKey] !== undefined;
                const value = hasValue ? config[paramKey] : null;

                let displayValue = '-';
                if (hasValue) {
                    const param = Object.values(CONFIG.CONFIG_PARAMS).find(p => p.key === paramKey);
                    if (param) {
                        if (param.type === 'boolean') {
                            displayValue = value ? '✅' : '❌';
                        } else if (param.type === 'select') {
                            const option = param.options.find(opt => opt.value === value);
                            displayValue = option ? option.label : value;
                        } else {
                            displayValue = value;
                        }
                    }
                }

                const isActive = hasValue;
                const className = isActive ? 'chain-level--active' : 'chain-level--inactive';

                return `
                    <div class="chain-level ${className}">
                        <span class="chain-level__icon">${level.icon}</span>
                        <span class="chain-level__label">${level.label}</span>
                        <span class="chain-level__value">${displayValue}</span>
                    </div>
                `;
            }).join('');
        }

        function showConfigModal(supplier) {
            const modal = document.getElementById('configModal');
            const title = document.getElementById('configModalTitle');
            const content = document.getElementById('configContent');
            
            title.textContent = `${supplier.supplierName} - 配置参数`;
            // 获取继承链
            const inheritanceChain = getInheritanceChain(supplier);

            content.innerHTML = `
                <div class="config-form">
                    <form id="configForm">
                        <div class="config-form__grid">
                            ${Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                                const paramConfig = resolveParameterConfig(supplier, param.key, inheritanceChain);

                                return `
                                    <div class="config-field">
                                        <div class="config-field__header">
                                            <label class="config-field__label">
                                                ${param.icon} ${param.label}
                                            </label>
                                        </div>
                                        <div class="config-field__chain">
                                            ${renderParameterChain(param.key, inheritanceChain)}
                                        </div>
                                        <div class="config-field__control">
                                            <label class="config-field__mode">
                                                <input type="radio" name="${param.key}_mode" value="custom" checked>
                                                自定义配置
                                            </label>
                                            <div class="config-field__input">
                                                ${renderConfigInput(param, supplier.config?.[param.key] || paramConfig.value)}
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                        <div class="config-form__actions">
                            <button type="button" class="btn btn--secondary" onclick="closeConfigModal()">取消</button>
                            <button type="submit" class="btn btn--primary">保存配置</button>
                        </div>
                    </form>
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // 绑定模式切换事件
            bindConfigModeEvents();
        }

        function renderConfigValue(param, value) {
            if (param.type === 'boolean') {
                return value ? '✅ 开启' : '❌ 关闭';
            } else if (param.type === 'select') {
                const option = param.options.find(opt => opt.value === value);
                return option ? option.label : value || '-';
            }
            return value || '-';
        }

        function renderConfigInput(param, value) {
            if (param.type === 'boolean') {
                return `
                    <label><input type="radio" name="${param.key}" value="true" ${value === true ? 'checked' : ''}> 开启</label>
                    <label><input type="radio" name="${param.key}" value="false" ${value === false ? 'checked' : ''}> 关闭</label>
                `;
            } else if (param.type === 'select') {
                return `
                    <select name="${param.key}" class="input">
                        ${param.options.map(opt => `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
                    </select>
                `;
            }
            return `<input type="text" name="${param.key}" class="input" value="${value || ''}">`;
        }

        function bindConfigModeEvents() {
            const modeInputs = document.querySelectorAll('input[name$="_mode"]');
            modeInputs.forEach(input => {
                input.addEventListener('change', (e) => {
                    const paramKey = e.target.name.replace('_mode', '');
                    const inputContainer = e.target.closest('.config-field').querySelector('.config-field__input');
                    const isCustom = e.target.value === 'custom';
                    
                    if (isCustom) {
                        inputContainer.classList.remove('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = false);
                    } else {
                        inputContainer.classList.add('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = true);
                    }
                });
            });
        }

        function closeConfigModal() {
            const modal = document.getElementById('configModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showInheritanceModal(supplier) {
            const modal = document.getElementById('inheritanceModal');
            const content = document.getElementById('inheritanceContent');
            
            // 获取业务方配置
            const factory = FACTORY_DATA.find(f => f.factoryCode === supplier.factoryCode);
            
            content.innerHTML = `
                <div class="inheritance-view">
                    <div class="inheritance-chain">
                        <div class="inheritance-level">
                            <div class="inheritance-level__header">
                                <h4>🏭 业务方级别配置</h4>
                                <span class="inheritance-level__code">${factory?.factoryCode} - ${factory?.factoryName}</span>
                            </div>
                            <div class="inheritance-level__config">
                                ${renderInheritanceConfig(factory?.config)}
                            </div>
                        </div>
                        <div class="inheritance-arrow">⬇️</div>
                        <div class="inheritance-level">
                            <div class="inheritance-level__header">
                                <h4>🏢 供应商级别配置</h4>
                                <span class="inheritance-level__code">${supplier.supplierCode} - ${supplier.supplierName}</span>
                            </div>
                            <div class="inheritance-level__config">
                                ${renderInheritanceConfig(supplier.config, factory?.config)}
                            </div>
                        </div>
                    </div>
                    <div class="inheritance-actions">
                        <button class="btn btn--secondary" onclick="closeInheritanceModal()">关闭</button>
                        <button class="btn btn--primary" onclick="configSupplier('${supplier.id}')">编辑配置</button>
                    </div>
                </div>
            `;
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function renderInheritanceConfig(config, inheritedConfig = null) {
            if (!config && !inheritedConfig) {
                return '<p class="inheritance-empty">未配置</p>';
            }
            
            return Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                const value = config?.[param.key];
                const inheritedValue = inheritedConfig?.[param.key];
                const isCustom = value !== null && value !== undefined;
                const displayValue = isCustom ? value : inheritedValue;
                
                let statusIcon = '';
                let statusText = '';
                let statusClass = '';
                
                if (isCustom) {
                    statusIcon = '🔧';
                    statusText = '自定义';
                    statusClass = 'config-status--custom';
                } else if (inheritedValue !== null && inheritedValue !== undefined) {
                    statusIcon = '🔗';
                    statusText = '继承';
                    statusClass = 'config-status--inherited';
                } else {
                    statusIcon = '⚠️';
                    statusText = '未配置';
                    statusClass = 'config-status--none';
                }
                
                return `
                    <div class="inheritance-param">
                        <div class="inheritance-param__header">
                            <span class="inheritance-param__icon">${param.icon}</span>
                            <span class="inheritance-param__label">${param.label}</span>
                            <span class="config-status ${statusClass}">${statusIcon} ${statusText}</span>
                        </div>
                        <div class="inheritance-param__value">
                            ${renderConfigValue(param, displayValue)}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function closeInheritanceModal() {
            const modal = document.getElementById('inheritanceModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showInheritanceView() {
            Utils.showMessage('继承关系总览功能开发中...', 'info');
        }

        function handleSupplierSubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            Utils.showMessage('供应商信息保存成功', 'success');
            closeSupplierModal();
            loadData();
        }

        function handleBatchInherit() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要设置继承的供应商', 'warning');
                return;
            }

            Utils.confirm(`确定要让选中的 ${selectedRows.length} 个供应商继承业务方配置吗？`, '批量继承确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已设置 ${selectedRows.length} 个供应商继承业务方配置`, 'success');
                    loadData();
                }
            });
        }

        function handleBatchDelete() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要删除的供应商', 'warning');
                return;
            }

            Utils.confirm(`确定要删除选中的 ${selectedRows.length} 个供应商吗？`, '批量删除确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已删除 ${selectedRows.length} 个供应商`, 'success');
                    loadData();
                }
            });
        }

        function handleExport() {
            Utils.showMessage('导出功能开发中...', 'info');
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSupplierModal();
                closeConfigModal();
                closeInheritanceModal();
            }
        });
    </script>

    <style>
        /* 供应商配置页面特定样式 */
        .inheritance-chain {
            color: var(--text-tertiary);
            font-size: var(--font-size-sm);
        }

        .config-source {
            font-size: var(--font-size-xs);
            padding: 2px 6px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }

        .config-source--custom {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
        }

        .config-source--inherited {
            background-color: var(--color-info-light);
            color: var(--color-info);
        }

        .config-form {
            max-height: 70vh;
            overflow-y: auto;
        }

        .config-form__header {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
        }

        .config-form__header h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
        }

        .config-form__header p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .config-field {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            border: 1px solid var(--border-secondary);
            border-radius: var(--border-radius-md);
        }

        .config-field__header {
            margin-bottom: var(--spacing-md);
        }

        .config-field__label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .config-field__inheritance {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
        }

        .inheritance-info {
            background-color: var(--bg-tertiary);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
        }

        .config-field__control {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .config-field__mode {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-sm);
            cursor: pointer;
        }

        .config-field__input {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-sm);
            transition: opacity 0.2s ease;
        }

        .config-field__input--disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .config-field__input label {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-sm);
        }

        .config-form__actions {
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-secondary);
            text-align: right;
        }

        .config-form__actions .btn {
            margin-left: var(--spacing-sm);
        }

        .inheritance-view {
            max-height: 70vh;
            overflow-y: auto;
        }

        .inheritance-chain {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .inheritance-level {
            border: 1px solid var(--border-secondary);
            border-radius: var(--border-radius-md);
            overflow: hidden;
        }

        .inheritance-level__header {
            background-color: var(--bg-secondary);
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
        }

        .inheritance-level__header h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
        }

        .inheritance-level__code {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .inheritance-level__config {
            padding: var(--spacing-md);
        }

        .inheritance-arrow {
            text-align: center;
            font-size: var(--font-size-lg);
            color: var(--text-tertiary);
        }

        .inheritance-param {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-tertiary);
        }

        .inheritance-param:last-child {
            border-bottom: none;
        }

        .inheritance-param__header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .inheritance-param__icon {
            font-size: var(--font-size-md);
        }

        .inheritance-param__label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .config-status {
            font-size: var(--font-size-xs);
            padding: 2px 6px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }

        .config-status--custom {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
        }

        .config-status--inherited {
            background-color: var(--color-info-light);
            color: var(--color-info);
        }

        .config-status--none {
            background-color: var(--color-warning-light);
            color: var(--color-warning);
        }

        .inheritance-param__value {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .inheritance-empty {
            text-align: center;
            color: var(--text-tertiary);
            font-style: italic;
            padding: var(--spacing-lg);
        }

        .inheritance-actions {
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-secondary);
            text-align: right;
        }

        .inheritance-actions .btn {
            margin-left: var(--spacing-sm);
        }

        @media (max-width: 767px) {
            .config-field {
                padding: var(--spacing-sm);
            }
            
            .config-field__input {
                flex-direction: column;
                align-items: stretch;
            }
            
            .inheritance-chain {
                gap: var(--spacing-sm);
            }
            
            .inheritance-param {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-xs);
            }
        }
    </style>
</body>
</html>
