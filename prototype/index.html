<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购协同配置中心 - 主导航</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="app">
        <!-- 主要内容区域 -->
        <main class="main">
            <div class="main__container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb__list">
                        <li class="breadcrumb__item">
                            <a href="#" class="breadcrumb__link">🏠 首页</a>
                        </li>
                        <li class="breadcrumb__item breadcrumb__item--current">
                            <span class="breadcrumb__text">配置管理</span>
                        </li>
                    </ol>
                </nav>

                <!-- 页面标题 -->
                <section class="page-header">
                    <h2 class="page-header__title">配置管理中心</h2>
                    <p class="page-header__description">
                        管理四级配置层次：业务方配置 → 供应商配置 → 品类配置 → 物料配置
                    </p>
                </section>

                <!-- 配置模块卡片 -->
                <section class="config-modules">
                    <div class="config-modules__grid">
                        <!-- 业务方配置模块 -->
                        <article class="config-card">
                            <div class="config-card__header">
                                <div class="config-card__icon">🏭</div>
                                <h3 class="config-card__title">业务方配置管理</h3>
                                <span class="config-card__level">Level 4 - 基础级别</span>
                            </div>
                            <div class="config-card__content">
                                <p class="config-card__description">
                                    管理业务方级别的基础配置参数，作为其他配置级别的默认值和回退配置。
                                </p>
                                <div class="config-card__stats">
                                    <div class="config-card__stat">
                                        <span class="config-card__stat-label">已配置业务方</span>
                                        <span class="config-card__stat-value">15</span>
                                    </div>
                                    <div class="config-card__stat">
                                        <span class="config-card__stat-label">未配置业务方</span>
                                        <span class="config-card__stat-value">8</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-card__actions">
                                <a href="factory-config.html" style="width: 100%;" class="btn btn--primary">
                                    进入管理
                                </a>
                            </div>
                        </article>

                        <!-- 供应商配置模块 -->
                        <article class="config-card">
                            <div class="config-card__header">
                                <div class="config-card__icon">🏪</div>
                                <h3 class="config-card__title">供应商配置管理</h3>
                                <span class="config-card__level">Level 3 - 供应商级别</span>
                            </div>
                            <div class="config-card__content">
                                <p class="config-card__description">
                                    管理特定业务方-供应商组合的配置参数，可继承业务方配置或自定义设置。
                                </p>
                                <div class="config-card__stats">
                                    <div class="config-card__stat">
                                        <span class="config-card__stat-label">已配置供应商</span>
                                        <span class="config-card__stat-value">45</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-card__actions">
                                <a href="supplier-config.html" style="width: 100%;" class="btn btn--primary">
                                    进入管理
                                </a>
                            </div>
                        </article>

                        <!-- 品类配置模块 -->
                        <article class="config-card">
                            <div class="config-card__header">
                                <div class="config-card__icon">📦</div>
                                <h3 class="config-card__title">品类配置管理</h3>
                                <span class="config-card__level">Level 2 - 品类级别</span>
                            </div>
                            <div class="config-card__content">
                                <br class="config-card__description">
                                    管理业务实体下品类的配置参数。</br></br>
                                </p>
                                <div class="config-card__stats">
                                    <div class="config-card__stat">
                                        <span class="config-card__stat-label">已配置品类</span>
                                        <span class="config-card__stat-value">12</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-card__actions">
                                <a href="category-config.html" style="width: 100%;" class="btn btn--primary">
                                    进入管理
                                </a>
                            </div>
                        </article>

                        <!-- 物料配置模块 -->
                        <article class="config-card">
                            <div class="config-card__header">
                                <div class="config-card__icon">🎯</div>
                                <h3 class="config-card__title">物料配置管理</h3>
                                <span class="config-card__level">Level 1 - 最高优先级</span>
                            </div>
                            <div class="config-card__content">
                                <p class="config-card__description">
                                    管理具体物料的配置参数，具有最高优先级，可覆盖所有下级配置。
                                </p>
                                <div class="config-card__stats">
                                    <div class="config-card__stat">
                                        <span class="config-card__stat-label">已配置物料</span>
                                        <span class="config-card__stat-value">89</span>
                                    </div>
                                </div>
                            </div>
                            <div class="config-card__actions">
                                <a href="material-config.html" style="width: 100%;" class="btn btn--primary">
                                    进入管理
                                </a>
                            </div>
                        </article>
                    </div>
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer__container">
                <p class="footer__text">
                    © 2024 上海电气风电集团有限公司 - 采购协同配置中心 v1.0
                </p>
            </div>
        </footer>
    </div>

    <!-- 快速统计模态框 -->
    <div id="quickStatsModal" class="modal" style="display: none;">
        <div class="modal__overlay" onclick="closeQuickStats()"></div>
        <div class="modal__content">
            <div class="modal__header">
                <h3 class="modal__title">快速统计</h3>
                <button class="modal__close" onclick="closeQuickStats()">×</button>
            </div>
            <div class="modal__body">
                <div id="quickStatsContent">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/data.js"></script>
    <script>
        // 显示快速统计
        function showQuickStats(type) {
            const modal = document.getElementById('quickStatsModal');
            const content = document.getElementById('quickStatsContent');
            
            const stats = {
                factory: {
                    title: '业务方配置统计',
                    data: [
                        { label: '总业务方数', value: '23', icon: '🏭' },
                        { label: '已配置', value: '15', icon: '✅' },
                        { label: '未配置', value: '8', icon: '⭕' },
                        { label: '配置完整度', value: '65.2%', icon: '📊' }
                    ]
                },
                supplier: {
                    title: '供应商配置统计',
                    data: [
                        { label: '总供应商数', value: '68', icon: '🏪' },
                        { label: '已配置', value: '45', icon: '✅' },
                    ]
                },
                category: {
                    title: '品类配置统计',
                    data: [
                        { label: '总品类数', value: '20', icon: '📦' },
                        { label: '已配置', value: '12', icon: '✅' },
                        { label: '配置完整度', value: '60.0%', icon: '📊' }
                    ]
                },
                material: {
                    title: '物料配置统计',
                    data: [
                        { label: '总物料数', value: '323', icon: '🎯' },
                        { label: '已配置', value: '89', icon: '✅' },
                        { label: '配置完整度', value: '27.6%', icon: '📊' }
                    ]
                }
            };

            const currentStats = stats[type];
            content.innerHTML = `
                <h4>${currentStats.title}</h4>
                <div class="stats-grid">
                    ${currentStats.data.map(item => `
                        <div class="stat-item">
                            <div class="stat-item__icon">${item.icon}</div>
                            <div class="stat-item__content">
                                <div class="stat-item__value">${item.value}</div>
                                <div class="stat-item__label">${item.label}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭快速统计
        function closeQuickStats() {
            const modal = document.getElementById('quickStatsModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQuickStats();
            }
        });
    </script>
</body>
</html>
