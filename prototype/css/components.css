/* 组件样式 - 采购协同配置中心 */

/* 应用布局 */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部导航 */
.header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-secondary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.header__logo {
    display: flex;
    flex-direction: column;
}

.header__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    line-height: 1.2;
}

.header__subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-top: 2px;
}

.header__nav {
    display: flex;
    align-items: center;
}

.header__user {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header__username {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.header__logout {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.header__logout:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 主要内容区域 */
.main {
    flex: 1;
    padding: var(--spacing-lg) 0;
}

.main__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: var(--spacing-lg);
}

.breadcrumb__list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.breadcrumb__item {
    display: flex;
    align-items: center;
}

.breadcrumb__item:not(:last-child)::after {
    content: '>';
    margin: 0 var(--spacing-sm);
    color: var(--text-tertiary);
}

.breadcrumb__link {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb__link:hover {
    color: var(--primary-color);
}

.breadcrumb__text {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 页面头部 */
.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-header__title {
    font-size: var(--font-size-xxl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.page-header__description {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    line-height: var(--line-height-lg);
}

.page-header__meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.page-header__update-time {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
}

/* 配置模块网格 */
.config-modules {
    margin-bottom: var(--spacing-xxl);
}

.config-modules__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* 配置卡片 */
.config-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.config-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.config-card__header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-primary) 100%);
}

.config-card__icon {
    font-size: 32px;
    margin-bottom: var(--spacing-sm);
}

.config-card__title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.config-card__level {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    font-weight: 500;
    background-color: rgba(24, 144, 255, 0.1);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius-sm);
}

.config-card__content {
    padding: var(--spacing-lg);
}

.config-card__description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-lg);
    margin-bottom: var(--spacing-md);
}

.config-card__stats {
    display: flex;
    gap: var(--spacing-lg);
}

.config-card__stat {
    text-align: center;
}

.config-card__stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-xs);
}

.config-card__stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.config-card__actions {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-secondary);
    background-color: var(--bg-secondary);
    display: flex;
    gap: var(--spacing-sm);
}

/* 系统概览 */
.system-overview {
    margin-bottom: var(--spacing-xxl);
}

.system-overview__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
}

.system-overview__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

/* 概览卡片 */
.overview-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all var(--transition-fast);
}

.overview-card:hover {
    box-shadow: var(--shadow-sm);
}

.overview-card__icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-light);
    border-radius: var(--border-radius-md);
}

.overview-card__content {
    flex: 1;
}

.overview-card__title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
}

.overview-card__value {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.overview-card__description {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin: 0;
}

/* 页脚 */
.footer {
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-secondary);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
}

.footer__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    text-align: center;
}

.footer__text {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin: 0;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    box-sizing: border-box;
}

.modal__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal__content {
    position: relative;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
    max-height: calc(100vh - 2 * var(--spacing-md));
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    margin: auto;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal__header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal__title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal__close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    font-size: 20px;
    color: var(--text-tertiary);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal__close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal__body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

/* 模态框响应式 */
@media (max-width: 768px) {
    .modal {
        padding: var(--spacing-sm);
    }

    .modal__content {
        max-width: none;
        width: 100%;
        max-height: calc(100vh - 2 * var(--spacing-sm));
    }

    .modal__header,
    .modal__body {
        padding: var(--spacing-md);
    }
}

/* 配置参数组件 - 紧凑样式 */
.config-params {
    padding: var(--spacing-sm);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
}

.config-params__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-secondary);
}

.config-params__header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-md);
}

.config-params__actions {
    display: flex;
    gap: var(--spacing-xs);
}

.config-params__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xs);
    row-gap: var(--spacing-xs);
}

.config-params__empty {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-tertiary);
}

.config-param {
    background-color: var(--bg-primary);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-sm);
    min-height: 32px;
}

.config-param__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0;
}

.config-param__icon {
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.config-param__label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.config-param__value {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: 2px var(--spacing-xs);
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius-xs);
    margin: 0 var(--spacing-xs);
    flex-shrink: 0;
    min-width: 60px;
    text-align: center;
}

.config-param__actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.config-source {
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    margin-left: auto;
    flex-shrink: 0;
}

.config-source--custom {
    background-color: var(--color-primary-light);
    color: var(--color-primary);
}

.config-source--inherited {
    background-color: var(--color-info-light);
    color: var(--color-info);
}

/* 配置参数响应式 */
@media (max-width: 767px) {
    .config-params__grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .config-param {
        flex-direction: column;
        align-items: stretch;
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .config-param__header {
        justify-content: flex-start;
    }

    .config-param__value {
        margin: 0;
        text-align: left;
        min-width: auto;
    }

    .config-param__actions {
        justify-content: flex-end;
    }
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.stat-item__icon {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
}

.stat-item__value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-item__label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 手风琴组件 */
.accordion {
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.accordion__item {
    border-bottom: 1px solid var(--border-secondary);
}

.accordion__item:last-child {
    border-bottom: none;
}

.accordion__header {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color var(--transition-fast);
}

.accordion__header:hover {
    background-color: var(--bg-tertiary);
}

.accordion__header--active {
    background-color: var(--primary-light);
}

.accordion__title {
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.accordion__icon {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    transition: transform var(--transition-normal);
}

.accordion__header--active .accordion__icon {
    transform: rotate(180deg);
}

.accordion__content {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal) ease;
}

.accordion__content--active {
    max-height: 1000px;
}

.accordion__body {
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
}

/* 搜索组件 */
.search {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.search__basic {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.search__field {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search__label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    white-space: nowrap;
}

.search__input {
    min-width: 150px;
}

.search__advanced {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-secondary);
}

.search__advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search__actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.toolbar__left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toolbar__right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 分页组件 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) 0;
    border-top: 1px solid var(--border-secondary);
    margin-top: var(--spacing-lg);
}

.pagination__info {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.pagination__controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pagination__button {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.pagination__button:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination__button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination__button--active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination__size {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}
