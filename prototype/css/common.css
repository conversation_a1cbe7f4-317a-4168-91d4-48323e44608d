/* 通用样式 - 采购协同配置中心 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    --primary-light: #e6f7ff;
    
    /* 辅助色 */
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;
    
    /* 中性色 */
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-tertiary: #8c8c8c;
    --text-disabled: #bfbfbf;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-disabled: #f5f5f5;
    
    /* 边框色 */
    --border-primary: #d9d9d9;
    --border-secondary: #f0f0f0;
    --border-hover: #40a9ff;
    
    /* 阴影 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    
    /* 圆角 */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 字体 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    /* 行高 */
    --line-height-sm: 1.2;
    --line-height-md: 1.5;
    --line-height-lg: 1.8;
    
    /* 过渡动画 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 重置样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 14px;
    line-height: var(--line-height-md);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
}

a:active {
    color: var(--primary-active);
}

/* 按钮基础样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 按钮变体 */
.btn--primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn--primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    color: white;
}

.btn--primary:active:not(:disabled) {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

.btn--secondary {
    background-color: var(--bg-primary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.btn--secondary:hover:not(:disabled) {
    border-color: var(--primary-hover);
    color: var(--primary-hover);
}

.btn--danger {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

.btn--danger:hover:not(:disabled) {
    background-color: #ff7875;
    border-color: #ff7875;
}

.btn--small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn--large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
}

/* 输入框样式 */
.input {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-md);
    background-color: var(--bg-primary);
    transition: border-color var(--transition-fast);
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.input:disabled {
    background-color: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.input--error {
    border-color: var(--error-color);
}

.input--error:focus {
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 选择框样式 */
.select {
    position: relative;
    display: inline-block;
}

.select__control {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-primary);
    cursor: pointer;
    transition: border-color var(--transition-fast);
    min-width: 120px;
}

.select__control:hover {
    border-color: var(--border-hover);
}

.select__control--open {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.select__value {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.select__arrow {
    margin-left: var(--spacing-sm);
    transition: transform var(--transition-fast);
}

.select__control--open .select__arrow {
    transform: rotate(180deg);
}

/* 卡片样式 */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card__header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    background-color: var(--bg-secondary);
}

.card__title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card__content {
    padding: var(--spacing-lg);
}

.card__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-secondary);
    background-color: var(--bg-secondary);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

.table__header {
    background-color: var(--bg-secondary);
}

.table__header-cell {
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
}

.table__row {
    transition: background-color var(--transition-fast);
}

.table__row:hover {
    background-color: var(--bg-tertiary);
}

.table__row--selected {
    background-color: var(--primary-light);
}

.table__cell {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-secondary);
    vertical-align: middle;
}

/* 状态标签 */
.status {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
}

.status--success {
    background-color: #f6ffed;
    color: var(--success-color);
    border: 1px solid #b7eb8f;
}

.status--warning {
    background-color: #fffbe6;
    color: var(--warning-color);
    border: 1px solid #ffe58f;
}

.status--error {
    background-color: #fff2f0;
    color: var(--error-color);
    border: 1px solid #ffccc7;
}

.status--info {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: 1px solid #91d5ff;
}

.status--default {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip__content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--spacing-sm);
    background-color: rgba(0, 0, 0, 0.85);
    color: white;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.tooltip:hover .tooltip__content {
    opacity: 1;
    visibility: visible;
}

/* 加载状态 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 辅助类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border { border: 1px solid var(--border-primary); }
.border-top { border-top: 1px solid var(--border-primary); }
.border-bottom { border-bottom: 1px solid var(--border-primary); }
.border-left { border-left: 1px solid var(--border-primary); }
.border-right { border-right: 1px solid var(--border-primary); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
