/* 响应式设计 - 采购协同配置中心 */

/* 断点定义 */
/* 超大屏: ≥1400px (4K显示器) */
/* 大屏: 1200px-1399px (桌面显示器) */
/* 中屏: 992px-1199px (小桌面/大平板) */
/* 小屏: 768px-991px (平板) */
/* 超小屏: <768px (手机) */

/* 超大屏适配 (≥1400px) */
@media (min-width: 1400px) {
    .main__container {
        max-width: 1400px;
    }
    
    .header__container {
        max-width: 1400px;
    }
    
    .footer__container {
        max-width: 1400px;
    }
    
    .config-modules__grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .system-overview__grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .table-container {
        overflow-x: visible;
    }
    
    .accordion__content--active {
        max-height: 1200px;
    }
}

/* 中屏适配 (992px-1199px) */
@media (max-width: 1199px) {
    .main__container {
        padding: 0 var(--spacing-md);
    }
    
    .header__container {
        padding: 0 var(--spacing-md);
    }
    
    .footer__container {
        padding: 0 var(--spacing-md);
    }
    
    .config-modules__grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .system-overview__grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* 表格适配 */
    .table__header-cell,
    .table__cell {
        padding: var(--spacing-sm);
    }
    
    /* 隐藏次要列 */
    .table__cell--secondary,
    .table__header-cell--secondary {
        display: none;
    }
    
    /* 搜索组件适配 */
    .search__advanced-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* 手风琴内容适配 */
    .config-params__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 小屏适配 (768px-991px) */
@media (max-width: 991px) {
    .header__container {
        height: 56px;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .header__title {
        font-size: var(--font-size-lg);
    }
    
    .header__subtitle {
        display: none;
    }
    
    .config-modules__grid {
        grid-template-columns: 1fr;
    }
    
    .system-overview__grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .page-header__title {
        font-size: var(--font-size-xl);
    }
    
    /* 工具栏适配 */
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar__left,
    .toolbar__right {
        justify-content: center;
    }
    
    /* 搜索组件适配 */
    .search__basic {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search__field {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }
    
    .search__advanced-grid {
        grid-template-columns: 1fr;
    }
    
    .search__actions {
        justify-content: center;
    }
    
    /* 分页适配 */
    .pagination {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .pagination__controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    /* 手风琴内容适配 */
    .config-params__grid {
        grid-template-columns: 1fr;
    }
    
    /* 模态框适配 */
    .modal__content {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 超小屏适配 (<768px) */
@media (max-width: 767px) {
    .main__container {
        padding: 0 var(--spacing-sm);
    }
    
    .header__container {
        padding: 0 var(--spacing-sm);
        height: 48px;
    }
    
    .footer__container {
        padding: 0 var(--spacing-sm);
    }
    
    .header__title {
        font-size: var(--font-size-md);
    }
    
    .header__user {
        gap: var(--spacing-sm);
    }
    
    .header__username {
        display: none;
    }
    
    /* 页面头部适配 */
    .page-header {
        margin-bottom: var(--spacing-lg);
        text-align: center;
    }
    
    .page-header__title {
        font-size: var(--font-size-lg);
    }
    
    .page-header__description {
        font-size: var(--font-size-sm);
    }
    
    /* 面包屑适配 */
    .breadcrumb__list {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    /* 系统概览适配 */
    .system-overview__grid {
        grid-template-columns: 1fr;
    }
    
    .overview-card {
        flex-direction: column;
        text-align: center;
    }
    
    .overview-card__icon {
        margin-bottom: var(--spacing-sm);
    }
    
    /* 表格适配 - 卡片式布局 */
    .table-container--mobile {
        display: block;
    }
    
    .table--mobile {
        display: none;
    }
    
    .mobile-cards {
        display: block;
    }
    
    .mobile-card {
        background-color: var(--bg-primary);
        border: 1px solid var(--border-secondary);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-card__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--border-secondary);
    }
    
    .mobile-card__title {
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-card__actions {
        display: flex;
        gap: var(--spacing-xs);
    }
    
    .mobile-card__content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }
    
    .mobile-card__field {
        display: flex;
        flex-direction: column;
    }
    
    .mobile-card__label {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        margin-bottom: 2px;
    }
    
    .mobile-card__value {
        font-size: var(--font-size-sm);
        color: var(--text-primary);
    }
    
    /* 按钮适配 */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        min-height: 44px;
    }
    
    .btn--small {
        padding: var(--spacing-xs) var(--spacing-sm);
        min-height: 36px;
    }
    
    /* 浮动操作按钮 */
    .fab {
        position: fixed;
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        border: none;
        box-shadow: var(--shadow-lg);
        font-size: 24px;
        cursor: pointer;
        z-index: 100;
        transition: all var(--transition-normal);
    }
    
    .fab:hover {
        background-color: var(--primary-hover);
        transform: scale(1.1);
    }
    
    .fab:active {
        transform: scale(0.95);
    }
    
    /* 手风琴移动端适配 */
    .accordion__header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .accordion__body {
        padding: var(--spacing-md);
    }
    
    .config-params__grid {
        gap: var(--spacing-md);
    }
    
    .config-param {
        padding: var(--spacing-md);
    }
    
    /* 搜索组件移动端适配 */
    .search {
        padding: var(--spacing-md);
    }
    
    .search__input {
        min-width: auto;
        width: 100%;
    }
    
    /* 模态框移动端适配 */
    .modal__content {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
        margin: 0;
    }
    
    .modal__header {
        padding: var(--spacing-md);
    }
    
    .modal__body {
        padding: var(--spacing-md);
        max-height: calc(100vh - 120px);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    /* 配置卡片移动端适配 */
    .config-card__header {
        padding: var(--spacing-md);
    }
    
    .config-card__content {
        padding: var(--spacing-md);
    }
    
    .config-card__stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .config-card__actions {
        padding: var(--spacing-md);
        flex-direction: column;
    }
}

/* 打印样式 */
@media print {
    .header,
    .footer,
    .toolbar,
    .pagination,
    .fab {
        display: none !important;
    }
    
    .main {
        padding: 0;
    }
    
    .main__container {
        max-width: none;
        padding: 0;
    }
    
    .page-header__title {
        color: black;
        font-size: 18pt;
    }
    
    .table {
        font-size: 10pt;
    }
    
    .config-card,
    .overview-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .accordion__content {
        max-height: none !important;
    }
    
    .btn {
        display: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #000000;
        --border-secondary: #666666;
        --text-primary: #000000;
        --text-secondary: #333333;
        --bg-primary: #ffffff;
        --bg-secondary: #f0f0f0;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .table__row:hover {
        background-color: #e0e0e0;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .accordion__content {
        transition: none;
    }
    
    .modal__content {
        animation: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #4096ff;
        --primary-hover: #69b1ff;
        --primary-active: #1677ff;
        --primary-light: #111b26;
        
        --text-primary: #ffffff;
        --text-secondary: #a6a6a6;
        --text-tertiary: #737373;
        --text-disabled: #595959;
        
        --bg-primary: #1f1f1f;
        --bg-secondary: #141414;
        --bg-tertiary: #262626;
        --bg-disabled: #262626;
        
        --border-primary: #434343;
        --border-secondary: #303030;
        --border-hover: #4096ff;
        
        --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
    }
    
    .status--success {
        background-color: #162312;
        color: #73d13d;
        border-color: #274916;
    }
    
    .status--warning {
        background-color: #2b2111;
        color: #fadb14;
        border-color: #613400;
    }
    
    .status--error {
        background-color: #2a1215;
        color: #ff7875;
        border-color: #58181c;
    }
    
    .status--info {
        background-color: #111b26;
        color: #4096ff;
        border-color: #1c3a5a;
    }
}
