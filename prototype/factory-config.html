<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务方配置管理 - 采购协同配置中心</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="app">
        <!-- 主要内容区域 -->
        <main class="main">
            <div class="main__container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb__list">
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">🏠 首页</a>
                        </li>
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">配置管理</a>
                        </li>
                        <li class="breadcrumb__item breadcrumb__item--current">
                            <span class="breadcrumb__text">业务方配置</span>
                        </li>
                    </ol>
                </nav>

                <!-- 页面标题 -->
                <section class="page-header">
                    <h2 class="page-header__title">🏭 业务方配置管理</h2>
                    <p class="page-header__description">
                        管理业务方级别的基础配置参数，作为其他配置级别的默认值和回退配置。
                    </p>
                    <div class="page-header__meta">
                        <span class="page-header__level">📊 配置级别: Level 4 - 基础级别</span>
                    </div>
                </section>

                <!-- 搜索筛选区域 -->
                <section class="search" id="searchContainer">
                    <div class="search__basic">
                        <div class="search__field">
                            <label class="search__label">🏭 业务方代码:</label>
                            <input type="text" name="factoryCode" class="input search__input" placeholder="F001">
                        </div>
                        <div class="search__field">
                            <button class="btn btn--primary search__btn">🔍 搜索</button>
                            <button class="btn btn--secondary search__reset">🔄 重置</button>
                        </div>
                    </div>
                </section>

                <!-- 工具栏 -->
                <section class="toolbar">
                    <div class="toolbar__left">
                        <button class="btn btn--primary" id="addFactoryBtn">➕ 新增业务方</button>
                        <button class="btn btn--secondary" id="batchConfigBtn" disabled>🔧 批量配置</button>
                        <button class="btn btn--danger" id="batchDeleteBtn" disabled>🗑️ 批量删除</button>
                    </div>
                    <div class="toolbar__right">
                        <button class="btn btn--secondary" id="exportBtn">📥 导入</button>
                        <button class="btn btn--secondary" id="exportBtn">📤 导出</button>
                    </div>
                </section>

                <!-- 数据表格 -->
                <section class="table-section">
                    <div id="tableContainer" class="table-container">
                        <!-- 表格内容将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 移动端卡片视图 -->
                    <div id="mobileCards" class="mobile-cards d-none">
                        <!-- 移动端卡片将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 分页 -->
                <section id="paginationContainer" class="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer__container">
                <p class="footer__text">
                    © 2024 上海电气风电集团有限公司 - 采购协同配置中心 v1.0
                </p>
            </div>
        </footer>
    </div>

    <!-- 新增/编辑业务方模态框 -->
    <div id="factoryModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 600px;">
            <div class="modal__header">
                <h3 class="modal__title" id="modalTitle">新增业务方</h3>
                <button class="modal__close" onclick="closeFactoryModal()">×</button>
            </div>
            <div class="modal__body">
                <form id="factoryForm">
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="form-field">
                            <label class="form-label">🏭 业务方代码 *</label>
                            <input type="text" name="factoryCode" class="input" required placeholder="F001">
                        </div>
                        <div class="form-field">
                            <label class="form-label">🏢 业务方名称 *</label>
                            <input type="text" name="factoryName" class="input" required placeholder="上海业务方">
                        </div>
                    </div>
                    <div class="form-field" style="margin-bottom: 16px;">
                        <label class="form-label">🏢 公司编号 *</label>
                        <input type="text" name="companyName" class="input" required placeholder="1502">
                    </div>
                    <div class="form-field" style="margin-bottom: 16px;">
                        <label class="form-label">🏢 公司名称 *</label>
                        <input type="text" name="companyName" class="input" required placeholder="上海电气风电集团">
                    </div>
                    <div class="form-actions" style="text-align: right; border-top: 1px solid #f0f0f0; padding-top: 16px;">
                        <button type="button" class="btn btn--secondary" onclick="closeFactoryModal()" style="margin-right: 8px;">取消</button>
                        <button type="submit" class="btn btn--primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量配置模态框 -->
    <div id="batchConfigModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 700px;">
            <div class="modal__header">
                <h3 class="modal__title">批量配置设置</h3>
                <button class="modal__close" onclick="closeBatchConfigModal()">×</button>
            </div>
            <div class="modal__body">
                <div id="batchConfigContent">
                    <!-- 批量配置内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 (移动端) -->
    <button class="fab d-none" id="mobileFab">+</button>

    <script src="js/common.js"></script>
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFactoryConfigPage();
        });

        // 全局变量
        let currentData = [];
        let currentFilters = {};
        let currentPage = 1;
        let currentPageSize = CONFIG.DEFAULT_PAGE_SIZE;
        let tableComponent = null;
        let searchComponent = null;
        let paginationComponent = null;
        let accordionComponent = null;

        // 初始化页面
        function initFactoryConfigPage() {
            initComponents();
            loadData();
            bindEvents();
            setupResponsive();
        }

        // 初始化组件
        function initComponents() {
            // 搜索组件
            searchComponent = new SearchComponent(document.getElementById('searchContainer'), {
                onSearch: handleSearch,
                onReset: handleReset
            });

            // 分页组件
            paginationComponent = new PaginationComponent(document.getElementById('paginationContainer'), {
                onChange: handlePageChange,
                onShowSizeChange: handlePageSizeChange
            });
        }

        // 加载数据
        function loadData() {
            const filters = {
                ...currentFilters,
                page: currentPage,
                pageSize: currentPageSize
            };

            const result = DataManager.getFactories(filters);
            currentData = result.data;

            renderTable();
            paginationComponent.update(result.total, result.page);
            updateToolbarState();
        }

        // 渲染表格
        function renderTable() {
            const columns = [
                {
                    title: '业务方代码',
                    dataIndex: 'factoryCode',
                    render: (value, record) => `<strong>${value}</strong>`
                },
                {
                    title: '业务方名称',
                    dataIndex: 'factoryName'
                },
                {
                    title: '公司编号｜名称',
                    dataIndex: 'companyName',
                    className: 'table__cell--secondary'
                },
                {
                    title: '配置状态',
                    dataIndex: 'configStatus',
                    render: (value, record) => {
                        const statusClass = record.status === 'CONFIGURED' ? 'status--success' : 'status--warning';
                        return `<span class="status ${statusClass}">${value}</span>`;
                    }
                },
                {
                    title: '更新时间',
                    dataIndex: 'updateTime',
                    className: 'table__cell--secondary'
                },
                {
                    title: '操作',
                    dataIndex: 'actions',
                    render: (value, record) => `
                        <div class="table__actions">
                            <button class="btn btn--small btn--primary" onclick="editFactory('${record.id}')">编辑</button>
                            <button class="btn btn--small btn--danger" onclick="deleteFactory('${record.id}')">删除</button>
                        </div>
                    `
                }
            ];

            // 检查是否为移动端
            if (window.innerWidth < 768) {
                renderMobileCards();
            } else {
                renderDesktopTable(columns);
            }
        }

        // 渲染桌面端表格
        function renderDesktopTable(columns) {
            document.getElementById('mobileCards').classList.add('d-none');
            document.getElementById('tableContainer').classList.remove('d-none');

            tableComponent = new TableComponent(document.getElementById('tableContainer'), {
                columns: columns,
                data: currentData,
                selectable: true,
                expandable: true,
                expandedRowRender: renderConfigParams,
                onSelectionChange: handleSelectionChange,
                onExpand: handleRowExpand
            });

            // 初始化手风琴组件
            setTimeout(() => {
                const accordionContainer = document.querySelector('.table-container');
                if (accordionContainer) {
                    accordionComponent = new AccordionComponent(accordionContainer);
                }
            }, 100);
        }

        // 渲染移动端卡片
        function renderMobileCards() {
            document.getElementById('tableContainer').classList.add('d-none');
            const mobileContainer = document.getElementById('mobileCards');
            mobileContainer.classList.remove('d-none');

            mobileContainer.innerHTML = currentData.map(record => `
                <div class="mobile-card" data-id="${record.id}">
                    <div class="mobile-card__header">
                        <div class="mobile-card__title">${record.factoryCode} - ${record.factoryName}</div>
                        <div class="mobile-card__actions">
                            <button class="btn btn--small btn--primary" onclick="editFactory('${record.id}')">编辑</button>
                            <button class="btn btn--small btn--secondary" onclick="configFactory('${record.id}')">配置</button>
                        </div>
                    </div>
                    <div class="mobile-card__content">
                        <div class="mobile-card__field">
                            <div class="mobile-card__label">公司名称</div>
                            <div class="mobile-card__value">${record.companyName}</div>
                        </div>
                        <div class="mobile-card__field">
                            <div class="mobile-card__label">配置状态</div>
                            <div class="mobile-card__value">
                                <span class="status ${record.status === 'CONFIGURED' ? 'status--success' : 'status--warning'}">
                                    ${record.configStatus}
                                </span>
                            </div>
                        </div>
                        <div class="mobile-card__field">
                            <div class="mobile-card__label">更新时间</div>
                            <div class="mobile-card__value">${record.updateTime}</div>
                        </div>
                    </div>
                </div>
            `).join('');

            // 显示浮动操作按钮
            document.getElementById('mobileFab').classList.remove('d-none');
        }

        // 渲染配置参数
        function renderConfigParams(record) {
            if (!record.config) {
                return `
                    <div class="config-params">
                        <div class="config-params__empty">
                            <p>🔧 该业务方尚未配置参数</p>
                            <button class="btn btn--primary" onclick="configFactory('${record.id}')">立即配置</button>
                        </div>
                    </div>
                `;
            }

            const params = Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                const value = record.config[param.key];
                let displayValue = '';
                
                if (param.type === 'boolean') {
                    displayValue = value ? '✅ 开启' : '❌ 关闭';
                } else if (param.type === 'select') {
                    const option = param.options.find(opt => opt.value === value);
                    displayValue = option ? option.label : value;
                } else {
                    displayValue = value || '';
                }

                return `
                    <div class="config-param">
                        <div class="config-param__header">
                            <span class="config-param__label">${param.label}</span>
                        </div>
                        <div class="config-param__value">${displayValue}</div>
                        <div class="config-param__actions">
                            <button class="btn btn--small btn--secondary" onclick="editParam('${record.id}', '${param.key}')">
                                编辑
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            return `
                <div class="config-params">
                    <div class="config-params__grid">
                        ${params}
                    </div>
                </div>
            `;
        }

        // 事件绑定
        function bindEvents() {
            // 新增业务方
            document.getElementById('addFactoryBtn').addEventListener('click', () => {
                showFactoryModal();
            });

            // 批量操作
            document.getElementById('batchConfigBtn').addEventListener('click', () => {
                showBatchConfigModal();
            });

            document.getElementById('batchDeleteBtn').addEventListener('click', () => {
                handleBatchDelete();
            });

            // 展开/收起
            document.getElementById('expandAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.expandAll();
                }
            });

            document.getElementById('collapseAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.collapseAll();
                }
            });

            // 其他操作
            document.getElementById('exportBtn').addEventListener('click', handleExport);
            document.getElementById('refreshBtn').addEventListener('click', loadData);

            // 移动端浮动按钮
            document.getElementById('mobileFab').addEventListener('click', () => {
                showFactoryModal();
            });

            // 业务方表单提交
            document.getElementById('factoryForm').addEventListener('submit', handleFactorySubmit);
        }

        // 搜索处理
        function handleSearch(filters) {
            currentFilters = filters;
            currentPage = 1;
            loadData();
        }

        // 重置处理
        function handleReset() {
            currentFilters = {};
            currentPage = 1;
            loadData();
        }

        // 分页处理
        function handlePageChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 页面大小改变处理
        function handlePageSizeChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 选择改变处理
        function handleSelectionChange(selectedRows) {
            updateToolbarState(selectedRows.length > 0);
        }

        // 行展开处理
        function handleRowExpand(record, expanded) {
            console.log('Row expand:', record.factoryCode, expanded);
        }

        // 更新工具栏状态
        function updateToolbarState(hasSelection = false) {
            document.getElementById('batchConfigBtn').disabled = !hasSelection;
            document.getElementById('batchDeleteBtn').disabled = !hasSelection;
        }

        // 响应式设置
        function setupResponsive() {
            window.addEventListener('resize', Utils.debounce(() => {
                renderTable();
            }, 300));
        }

        // 业务方操作函数
        function editFactory(id) {
            const factory = currentData.find(item => item.id === id);
            if (factory) {
                showFactoryModal(factory);
            }
        }

        function configFactory(id) {
            const factory = currentData.find(item => item.id === id);
            if (factory) {
                // 跳转到配置页面或显示配置模态框
                Utils.showMessage(`配置业务方: ${factory.factoryName}`, 'info');
            }
        }

        function deleteFactory(id) {
            const factory = currentData.find(item => item.id === id);
            if (factory) {
                Utils.confirm(`确定要删除业务方 "${factory.factoryName}" 吗？`, '删除确认').then(confirmed => {
                    if (confirmed) {
                        // 执行删除操作
                        Utils.showMessage(`已删除业务方: ${factory.factoryName}`, 'success');
                        loadData();
                    }
                });
            }
        }

        // 模态框操作
        function showFactoryModal(factory = null) {
            const modal = document.getElementById('factoryModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('factoryForm');
            
            if (factory) {
                title.textContent = '编辑业务方';
                form.factoryCode.value = factory.factoryCode;
                form.factoryName.value = factory.factoryName;
                form.companyName.value = factory.companyName;
                form.factoryCode.readOnly = true;
            } else {
                title.textContent = '新增业务方';
                form.reset();
                form.factoryCode.readOnly = false;
            }
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeFactoryModal() {
            const modal = document.getElementById('factoryModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function handleFactorySubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 模拟保存操作
            Utils.showMessage('业务方信息保存成功', 'success');
            closeFactoryModal();
            loadData();
        }

        // 批量配置
        function showBatchConfigModal() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要配置的业务方', 'warning');
                return;
            }

            const modal = document.getElementById('batchConfigModal');
            const content = document.getElementById('batchConfigContent');
            
            content.innerHTML = `
                <p>📊 将对 ${selectedRows.length} 个业务方应用以下配置:</p>
                <div class="batch-config-form">
                    ${Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => `
                        <div class="config-field">
                            <label class="config-field__label">
                                ${param.icon} ${param.label}:
                            </label>
                            <div class="config-field__control">
                                ${renderConfigControl(param)}
                                <label class="config-field__keep">
                                    <input type="checkbox" name="keep_${param.key}" checked> 保持不变
                                </label>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="batch-config-actions" style="margin-top: 24px; text-align: right; border-top: 1px solid #f0f0f0; padding-top: 16px;">
                    <button class="btn btn--secondary" onclick="closeBatchConfigModal()" style="margin-right: 8px;">取消</button>
                    <button class="btn btn--primary" onclick="applyBatchConfig()">应用配置</button>
                </div>
            `;
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function renderConfigControl(param) {
            if (param.type === 'boolean') {
                return `
                    <label><input type="radio" name="${param.key}" value="true"> 开启</label>
                    <label><input type="radio" name="${param.key}" value="false"> 关闭</label>
                `;
            } else if (param.type === 'select') {
                return `
                    <select name="${param.key}" class="input">
                        <option value="">请选择</option>
                        ${param.options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('')}
                    </select>
                `;
            }
            return `<input type="text" name="${param.key}" class="input">`;
        }

        function closeBatchConfigModal() {
            const modal = document.getElementById('batchConfigModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function applyBatchConfig() {
            Utils.showMessage('批量配置应用成功', 'success');
            closeBatchConfigModal();
            loadData();
        }

        // 批量删除
        function handleBatchDelete() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要删除的业务方', 'warning');
                return;
            }

            Utils.confirm(`确定要删除选中的 ${selectedRows.length} 个业务方吗？`, '批量删除确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已删除 ${selectedRows.length} 个业务方`, 'success');
                    loadData();
                }
            });
        }

        // 导出
        function handleExport() {
            Utils.showMessage('导出功能开发中...', 'info');
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFactoryModal();
                closeBatchConfigModal();
            }
        });
    </script>

    <style>
        /* 页面特定样式 */

        .config-params__empty {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-tertiary);
        }

        .config-params__empty p {
            margin-bottom: var(--spacing-md);
            font-size: var(--font-size-md);
        }

        .table__actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-field {
            margin-bottom: var(--spacing-md);
        }

        .batch-config-form {
            display: grid;
            gap: var(--spacing-md);
        }

        .config-field {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: var(--spacing-md);
            align-items: center;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-secondary);
            border-radius: var(--border-radius-sm);
        }

        .config-field__label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .config-field__control {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .config-field__control label {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-sm);
        }

        .config-field__keep {
            margin-left: auto;
            color: var(--text-tertiary);
        }

        @media (max-width: 767px) {
            .config-field {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }

            .config-field__control {
                flex-wrap: wrap;
            }
        }
    </style>
</body>
</html>
