<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料配置管理 - 采购协同配置中心</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="app">
        <!-- 主要内容区域 -->
        <main class="main">
            <div class="main__container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb__list">
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">🏠 首页</a>
                        </li>
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">配置管理</a>
                        </li>
                        <li class="breadcrumb__item breadcrumb__item--current">
                            <span class="breadcrumb__text">物料配置</span>
                        </li>
                    </ol>
                </nav>

                <!-- 页面标题 -->
                <section class="page-header">
                    <h2 class="page-header__title">📦 物料配置管理</h2>
                    <p class="page-header__description">
                        管理物料级别的配置参数，具有最高优先级。配置优先级：<strong>物料级别</strong> > 品类级别 > 供应商级别 > 业务方级别。
                    </p>
                    <div class="page-header__meta">
                        <span class="page-header__level">📊 配置级别: Level 1 - 物料级别 (最高优先级)</span>
                    </div>
                </section>

                <!-- 搜索筛选区域 -->
                <section class="search" id="searchContainer">
                    <div class="search__basic">
                        <div class="search__field">
                            <label class="search__label">🔍 关键词:</label>
                            <input type="text" name="keyword" class="input search__input" placeholder="物料代码、物料名称">
                        </div>
                        <div class="search__field">
                            <label class="search__label">📂 品类:</label>
                            <select name="categoryCode" class="input search__input">
                                <option value="">全部品类</option>
                                <!-- 品类选项将通过JavaScript动态生成 -->
                            </select>
                        </div>
                        <div class="search__field">
                            <button class="btn btn--primary search__btn">🔍 搜索</button>
                        </div>
                    </div>

                    <div class="search__advanced" style="display: none;">
                        <div class="search__advanced-grid">
                            <div class="search__field">
                                <label class="search__label">📦 物料代码:</label>
                                <input type="text" name="materialCode" class="input search__input" placeholder="M001">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 创建时间:</label>
                                <input type="date" name="createTimeStart" class="input search__input">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 至:</label>
                                <input type="date" name="createTimeEnd" class="input search__input">
                            </div>
                            <div class="search__field">
                                <label class="search__label">🔧 配置来源:</label>
                                <select name="configSource" class="input search__input">
                                    <option value="">全部来源</option>
                                    <option value="MATERIAL">物料自定义</option>
                                    <option value="CATEGORY">品类继承</option>
                                    <option value="SUPPLIER">供应商继承</option>
                                    <option value="FACTORY">业务方继承</option>
                                </select>
                            </div>
                        </div>
                        <div class="search__actions">
                            <button class="btn btn--primary">🔍 高级搜索</button>
                            <button class="btn btn--secondary">📋 保存条件</button>
                            <button class="btn btn--secondary">📂 加载预设</button>
                        </div>
                    </div>
                </section>

                <!-- 工具栏 -->
                <section class="toolbar">
                    <div class="toolbar__left">
                        <button class="btn btn--primary" id="addMaterialBtn">➕ 新增物料</button>
                        <button class="btn btn--secondary" id="batchConfigBtn" disabled>🔧 批量配置</button>
                        <button class="btn btn--danger" id="batchDeleteBtn" disabled>🗑️ 批量删除</button>
                    </div>
                    <div class="toolbar__right">
                        <button class="btn btn--secondary" id="exportBtn">📥 导入</button>
                        <button class="btn btn--secondary" id="exportBtn">📤 导出</button>
                        </div>
                </section>

                <!-- 数据表格 -->
                <section class="table-section">
                    <div id="tableContainer" class="table-container">
                        <!-- 表格内容将通过JavaScript动态生成 -->
                    </div>

                    <!-- 移动端卡片视图 -->
                    <div id="mobileCards" class="mobile-cards d-none">
                        <!-- 移动端卡片将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 分页 -->
                <section id="paginationContainer" class="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </section>
            </div>
        </main>

        <!-- 新增/编辑物料模态框 -->
        <div id="materialModal" class="modal" style="display: none;">
            <div class="modal__overlay"></div>
            <div class="modal__content" style="max-width: 700px;">
                <div class="modal__header">
                    <h3 class="modal__title" id="modalTitle">新增物料</h3>
                    <button class="modal__close" onclick="closeMaterialModal()">×</button>
                </div>
                <div class="modal__body">
                    <form id="materialForm">
                        <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-field">
                                <label class="form-label">📦 物料代码 *</label>
                                <input type="text" name="materialCode" class="input" required placeholder="M001">
                            </div>
                            <div class="form-field">
                                <label class="form-label">📦 物料名称 *</label>
                                <input type="text" name="materialName" class="input" required placeholder="风机叶片">
                            </div>
                        </div>
                        <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div class="form-field">
                                <label class="form-label">🏭 业务方</label>
                                <select name="factoryCode" class="input">
                                    <option value="">请选择业务方</option>
                                    <!-- 业务方选项将通过JavaScript动态生成 -->
                                </select>
                            </div>
                            <div class="form-field">
                                <label class="form-label">📋 规格型号</label>
                                <input type="text" name="specification" class="input" placeholder="2.5MW-116">
                            </div>
                        </div>
                        <div class="form-actions" style="text-align: right; border-top: 1px solid #f0f0f0; padding-top: 16px;">
                            <button type="button" class="btn btn--secondary" onclick="closeMaterialModal()" style="margin-right: 8px;">取消</button>
                            <button type="submit" class="btn btn--primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 配置参数模态框 -->
        <div id="configModal" class="modal" style="display: none;">
            <div class="modal__overlay"></div>
            <div class="modal__content" style="max-width: 900px;">
                <div class="modal__header">
                    <h3 class="modal__title" id="configModalTitle">物料配置</h3>
                    <button class="modal__close" onclick="closeConfigModal()">×</button>
                </div>
                <div class="modal__body">
                    <div id="configContent">
                        <!-- 配置内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 继承链可视化模态框 -->
        <div id="inheritanceModal" class="modal" style="display: none;">
            <div class="modal__overlay"></div>
            <div class="modal__content" style="max-width: 1000px;">
                <div class="modal__header">
                    <h3 class="modal__title">🔗 配置继承链可视化</h3>
                    <button class="modal__close" onclick="closeInheritanceModal()">×</button>
                </div>
                <div class="modal__body">
                    <div id="inheritanceContent">
                        <!-- 继承链内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置分析模态框 -->
        <div id="analysisModal" class="modal" style="display: none;">
            <div class="modal__overlay"></div>
            <div class="modal__content" style="max-width: 1000px;">
                <div class="modal__header">
                    <h3 class="modal__title">📊 配置分析报告</h3>
                    <button class="modal__close" onclick="closeAnalysisModal()">×</button>
                </div>
                <div class="modal__body">
                    <div id="analysisContent">
                        <!-- 分析内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 浮动操作按钮 (移动端) -->
        <button class="fab d-none" id="mobileFab">+</button>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer__container">
                <p class="footer__text">
                    © 2024 上海电气风电集团有限公司 - 采购协同配置中心 v1.0
                </p>
            </div>
        </footer>
    </div>

    <script src="js/common.js"></script>
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMaterialConfigPage();
        });

        // 全局变量
        let currentData = [];
        let currentFilters = {};
        let currentPage = 1;
        let currentPageSize = CONFIG.DEFAULT_PAGE_SIZE;
        let tableComponent = null;
        let searchComponent = null;
        let paginationComponent = null;
        let accordionComponent = null;
        let selectedMaterials = new Set();

        // 初始化页面
        function initMaterialConfigPage() {
            initComponents();
            loadData();
            bindEvents();
            setupResponsive();
            populateSelectOptions();
        }

        // 初始化组件
        function initComponents() {
            // 搜索组件
            searchComponent = new SearchComponent(document.getElementById('searchContainer'), {
                onSearch: handleSearch,
                onReset: handleReset
            });

            // 分页组件
            paginationComponent = new PaginationComponent(document.getElementById('paginationContainer'), {
                onChange: handlePageChange,
                onShowSizeChange: handlePageSizeChange
            });
        }

        // 填充下拉选项
        function populateSelectOptions() {
            // 填充品类选项
            const categorySelects = document.querySelectorAll('select[name="categoryCode"]');
            const categoryOptions = CATEGORY_DATA.map(cat =>
                `<option value="${cat.categoryCode}">${cat.categoryCode} - ${cat.categoryName}</option>`
            ).join('');
            categorySelects.forEach(select => {
                if (select.querySelector('option[value=""]')) {
                    select.innerHTML = select.querySelector('option[value=""]').outerHTML + categoryOptions;
                }
            });

            // 填充供应商选项
            const supplierSelects = document.querySelectorAll('select[name="supplierCode"]');
            const supplierOptions = SUPPLIER_DATA.map(sup =>
                `<option value="${sup.supplierCode}">${sup.supplierCode} - ${sup.supplierName}</option>`
            ).join('');
            supplierSelects.forEach(select => {
                if (select.querySelector('option[value=""]')) {
                    select.innerHTML = select.querySelector('option[value=""]').outerHTML + supplierOptions;
                }
            });

            // 填充业务方选项
            const factorySelects = document.querySelectorAll('select[name="factoryCode"]');
            const factoryOptions = FACTORY_DATA.map(fac =>
                `<option value="${fac.factoryCode}">${fac.factoryCode} - ${fac.factoryName}</option>`
            ).join('');
            factorySelects.forEach(select => {
                if (select.querySelector('option[value=""]')) {
                    select.innerHTML = select.querySelector('option[value=""]').outerHTML + factoryOptions;
                }
            });
        }

        // 加载数据
        function loadData() {
            const filters = {
                ...currentFilters,
                page: currentPage,
                pageSize: currentPageSize
            };

            const result = DataManager.getMaterials(filters);
            currentData = result.data;

            renderTableView();
            paginationComponent.update(result.total, result.page);
            updateToolbarState();
        }

        // 渲染表格视图
        function renderTableView() {
            const columns = [
                {
                    title: '物料代码',
                    dataIndex: 'materialCode',
                    render: (value, record) => `
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 8px;">📦</span>
                            <strong>${value}</strong>
                        </div>
                    `
                },
                {
                    title: '物料名称',
                    dataIndex: 'materialName'
                },
                {
                    title: '品类',
                    dataIndex: 'categoryCode',
                    render: (value, record) => {
                        const category = CATEGORY_DATA.find(c => c.categoryCode === value);
                        return category ? `${value} - ${category.categoryName}` : value;
                    }
                },
                {
                    title: '更新时间',
                    dataIndex: 'updateTime',
                    className: 'table__cell--secondary'
                },
                {
                    title: '操作',
                    dataIndex: 'actions',
                    render: (value, record) => `
                        <div class="table__actions">
                            <button class="btn btn--small btn--primary" onclick="editMaterial('${record.id}')">编辑</button>
                            <button class="btn btn--small btn--secondary" onclick="configMaterial('${record.id}')">配置</button>
                            <button class="btn btn--small btn--info" onclick="viewInheritance('${record.id}')">继承链</button>
                            <button class="btn btn--small btn--danger" onclick="deleteMaterial('${record.id}')">删除</button>
                        </div>
                    `
                }
            ];

            // 检查是否为移动端
            if (window.innerWidth < 768) {
                renderMobileCards();
            } else {
                renderDesktopTable(columns);
            }
        }

        // 渲染桌面端表格
        function renderDesktopTable(columns) {
            document.getElementById('mobileCards').classList.add('d-none');
            document.getElementById('tableContainer').classList.remove('d-none');

            tableComponent = new TableComponent(document.getElementById('tableContainer'), {
                columns: columns,
                data: currentData,
                selectable: true,
                expandable: true,
                expandedRowRender: renderConfigParams,
                onSelectionChange: handleSelectionChange,
                onExpand: handleRowExpand
            });

            // 初始化手风琴组件
            setTimeout(() => {
                const accordionContainer = document.querySelector('.table-container');
                if (accordionContainer) {
                    accordionComponent = new AccordionComponent(accordionContainer);
                }
            }, 100);
        }

        // 渲染移动端卡片
        function renderMobileCards() {
            document.getElementById('tableContainer').classList.add('d-none');
            const mobileContainer = document.getElementById('mobileCards');
            mobileContainer.classList.remove('d-none');

            mobileContainer.innerHTML = currentData.map(record => {
                let statusIcon = '';
                switch (record.status) {
                    case 'CONFIGURED': statusIcon = '🔧'; break;
                    case 'INHERITED': statusIcon = '🔗'; break;
                    default: statusIcon = '⚠️';
                }

                const sourceMap = {
                    'MATERIAL': { icon: '📦', label: '物料自定义' },
                    'CATEGORY': { icon: '📂', label: '品类继承' },
                    'SUPPLIER': { icon: '🏢', label: '供应商继承' },
                    'FACTORY': { icon: '🏭', label: '业务方继承' }
                };
                const source = sourceMap[record.configSource] || { icon: '❓', label: record.configSource };

                return `
                    <div class="mobile-card" data-id="${record.id}">
                        <div class="mobile-card__header">
                            <div class="mobile-card__title">
                                📦 ${record.materialCode} - ${record.materialName}
                            </div>
                            <div class="mobile-card__actions">
                                <button class="btn btn--small btn--primary" onclick="editMaterial('${record.id}')">编辑</button>
                                <button class="btn btn--small btn--secondary" onclick="configMaterial('${record.id}')">配置</button>
                            </div>
                        </div>
                        <div class="mobile-card__content">
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">品类</div>
                                <div class="mobile-card__value">${record.categoryCode}</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">更新时间</div>
                                <div class="mobile-card__value">${record.updateTime}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // 显示浮动操作按钮
            document.getElementById('mobileFab').classList.remove('d-none');
        }

        // 渲染配置参数
        function renderConfigParams(record) {
            // 获取完整的继承链配置
            const inheritanceChain = getInheritanceChain(record);

            if (!record.config && record.status !== 'INHERITED') {
                return `
                    <div class="config-params">
                        <div class="config-params__empty">
                            <p>🔧 该物料尚未配置参数</p>
                            <button class="btn btn--primary" onclick="configMaterial('${record.id}')">立即配置</button>
                            <button class="btn btn--secondary" onclick="inheritFromChain('${record.id}')">继承配置</button>
                        </div>
                    </div>
                `;
            }

            const params = Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                const paramConfig = resolveParameterConfig(record, param.key, inheritanceChain);

                const sourceIcon = paramConfig.source === 'MATERIAL' ? '🔧' : '🔗';
                const sourceText = paramConfig.source === 'MATERIAL' ? '自定义' : `继承自${paramConfig.sourceLevel}`;
                const sourceClass = paramConfig.source === 'MATERIAL' ? 'config-source--custom' : 'config-source--inherited';

                return `
                    <div class="config-param">
                        <div class="config-param__header">
                            <span class="config-param__icon">${param.icon}</span>
                            <span class="config-param__label">${param.label}</span>
                            <span class="config-source ${sourceClass}">${sourceIcon} ${sourceText}</span>
                        </div>
                        <div class="config-param__value">${paramConfig.displayValue}</div>
                        <div class="config-param__chain">
                            ${renderParameterChain(param.key, inheritanceChain)}
                        </div>
                        <div class="config-param__actions">
                            <button class="btn btn--small btn--secondary" onclick="editParam('${record.id}', '${param.key}')">
                                编辑
                            </button>
                            ${paramConfig.source === 'MATERIAL' ? `
                                <button class="btn btn--small btn--tertiary" onclick="resetToInherit('${record.id}', '${param.key}')">
                                    重置继承
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            return `
                <div class="config-params">
                    <div class="config-params__header">
                        <h4>📋 配置参数</h4>
                        <div class="config-params__actions">
                            <button class="btn btn--small btn--primary" onclick="configMaterial('${record.id}')">编辑配置</button>
                            <button class="btn btn--small btn--secondary" onclick="copyConfig('${record.id}')">复制配置</button>
                            <button class="btn btn--small btn--info" onclick="viewInheritance('${record.id}')">继承链</button>
                        </div>
                    </div>
                    <div class="config-params__grid">
                        ${params}
                    </div>
                </div>
            `;
        }

        // 获取继承链
        function getInheritanceChain(material) {
            const chain = {
                factory: null,
                supplier: null,
                category: null,
                material: material
            };

            // 获取业务方配置
            if (material.factoryCode) {
                chain.factory = FACTORY_DATA.find(f => f.factoryCode === material.factoryCode);
            }

            // 获取供应商配置
            if (material.supplierCode) {
                chain.supplier = SUPPLIER_DATA.find(s => s.supplierCode === material.supplierCode);
            }

            // 获取品类配置
            if (material.categoryCode) {
                chain.category = CATEGORY_DATA.find(c => c.categoryCode === material.categoryCode);
            }

            return chain;
        }

        // 解析参数配置
        function resolveParameterConfig(material, paramKey, chain) {
            let value, source, sourceLevel, displayValue;

            // 按优先级检查配置
            if (material.config && material.config[paramKey] !== null && material.config[paramKey] !== undefined) {
                value = material.config[paramKey];
                source = 'MATERIAL';
                sourceLevel = '物料';
            } else if (chain.category && chain.category.config && chain.category.config[paramKey] !== null && chain.category.config[paramKey] !== undefined) {
                value = chain.category.config[paramKey];
                source = 'CATEGORY';
                sourceLevel = '品类';
            } else if (chain.supplier && chain.supplier.config && chain.supplier.config[paramKey] !== null && chain.supplier.config[paramKey] !== undefined) {
                value = chain.supplier.config[paramKey];
                source = 'SUPPLIER';
                sourceLevel = '供应商';
            } else if (chain.factory && chain.factory.config && chain.factory.config[paramKey] !== null && chain.factory.config[paramKey] !== undefined) {
                value = chain.factory.config[paramKey];
                source = 'FACTORY';
                sourceLevel = '业务方';
            } else {
                value = null;
                source = 'NONE';
                sourceLevel = '未配置';
            }

            // 格式化显示值
            const param = Object.values(CONFIG.CONFIG_PARAMS).find(p => p.key === paramKey);
            if (param) {
                if (param.type === 'boolean') {
                    displayValue = value ? '✅ 开启' : '❌ 关闭';
                } else if (param.type === 'select') {
                    const option = param.options.find(opt => opt.value === value);
                    displayValue = option ? option.label : value;
                } else {
                    displayValue = value || '-';
                }
            } else {
                displayValue = value || '-';
            }

            return { value, source, sourceLevel, displayValue };
        }

        // 渲染参数继承链
        function renderParameterChain(paramKey, chain) {
            const levels = [
                { key: 'factory', label: '业务方', icon: '🏭', data: chain.factory },
                { key: 'supplier', label: '供应商', icon: '🏢', data: chain.supplier },
                { key: 'category', label: '品类', icon: '📂', data: chain.category },
                { key: 'material', label: '物料', icon: '📦', data: chain.material }
            ];

            return levels.map(level => {
                if (!level.data) return '';

                const config = level.data.config;
                const hasValue = config && config[paramKey] !== null && config[paramKey] !== undefined;
                const value = hasValue ? config[paramKey] : null;

                let displayValue = '-';
                if (hasValue) {
                    const param = Object.values(CONFIG.CONFIG_PARAMS).find(p => p.key === paramKey);
                    if (param) {
                        if (param.type === 'boolean') {
                            displayValue = value ? '✅' : '❌';
                        } else if (param.type === 'select') {
                            const option = param.options.find(opt => opt.value === value);
                            displayValue = option ? option.label : value;
                        } else {
                            displayValue = value;
                        }
                    }
                }

                const isActive = hasValue;
                const className = isActive ? 'chain-level--active' : 'chain-level--inactive';

                return `
                    <div class="chain-level ${className}">
                        <span class="chain-level__icon">${level.icon}</span>
                        <span class="chain-level__label">${level.label}</span>
                        <span class="chain-level__value">${displayValue}</span>
                    </div>
                `;
            }).join('');
        }

        // 事件绑定
        function bindEvents() {
            // 新增物料
            document.getElementById('addMaterialBtn').addEventListener('click', () => {
                showMaterialModal();
            });

            // 批量操作
            document.getElementById('batchConfigBtn').addEventListener('click', () => {
                showBatchConfigModal();
            });

            document.getElementById('batchInheritBtn').addEventListener('click', () => {
                handleBatchInherit();
            });

            document.getElementById('batchDeleteBtn').addEventListener('click', () => {
                handleBatchDelete();
            });

            // 其他操作
            document.getElementById('inheritanceViewBtn').addEventListener('click', () => {
                showInheritanceViewModal();
            });

            document.getElementById('configAnalysisBtn').addEventListener('click', () => {
                showConfigAnalysisModal();
            });

            document.getElementById('exportBtn').addEventListener('click', handleExport);
            document.getElementById('refreshBtn').addEventListener('click', loadData);

            // 移动端浮动按钮
            document.getElementById('mobileFab').addEventListener('click', () => {
                showMaterialModal();
            });

            // 物料表单提交
            document.getElementById('materialForm').addEventListener('submit', handleMaterialSubmit);
        }

        // 搜索处理
        function handleSearch(filters) {
            currentFilters = filters;
            currentPage = 1;
            loadData();
        }

        // 重置处理
        function handleReset() {
            currentFilters = {};
            currentPage = 1;
            loadData();
        }

        // 分页处理
        function handlePageChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 页面大小改变处理
        function handlePageSizeChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 选择改变处理
        function handleSelectionChange(selectedRows) {
            updateToolbarState(selectedRows.length > 0);
        }

        // 行展开处理
        function handleRowExpand(record, expanded) {
            console.log('Row expand:', record.materialCode, expanded);
        }

        // 更新工具栏状态
        function updateToolbarState(hasSelection = false) {
            document.getElementById('batchConfigBtn').disabled = !hasSelection;
            document.getElementById('batchInheritBtn').disabled = !hasSelection;
            document.getElementById('batchDeleteBtn').disabled = !hasSelection;
        }

        // 响应式设置
        function setupResponsive() {
            window.addEventListener('resize', Utils.debounce(() => {
                renderTableView();
            }, 300));
        }

        // 物料操作函数
        function editMaterial(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                showMaterialModal(material);
            }
        }

        function configMaterial(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                showConfigModal(material);
            }
        }

        function viewInheritance(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                showInheritanceModal(material);
            }
        }

        function copyConfig(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                Utils.showMessage(`已复制物料 "${material.materialName}" 的配置`, 'success');
            }
        }

        function deleteMaterial(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                Utils.confirm(`确定要删除物料 "${material.materialName}" 吗？`, '删除确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已删除物料: ${material.materialName}`, 'success');
                        loadData();
                    }
                });
            }
        }

        function inheritFromChain(id) {
            const material = currentData.find(item => item.id === id);
            if (material) {
                Utils.confirm(`确定要让物料 "${material.materialName}" 继承配置链吗？`, '继承确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已设置为继承配置链`, 'success');
                        loadData();
                    }
                });
            }
        }

        // 模态框操作
        function showMaterialModal(material = null) {
            const modal = document.getElementById('materialModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('materialForm');

            if (material) {
                title.textContent = '编辑物料';
                form.materialCode.value = material.materialCode;
                form.materialName.value = material.materialName;
                form.categoryCode.value = material.categoryCode;
                form.supplierCode.value = material.supplierCode || '';
                form.factoryCode.value = material.factoryCode || '';
                form.specification.value = material.specification || '';
                form.materialCode.readOnly = true;
            } else {
                title.textContent = '新增物料';
                form.reset();
                form.materialCode.readOnly = false;
            }

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeMaterialModal() {
            const modal = document.getElementById('materialModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showConfigModal(material) {
            const modal = document.getElementById('configModal');
            const title = document.getElementById('configModalTitle');
            const content = document.getElementById('configContent');

            title.textContent = `${material.materialName} - 配置参数`;

            // 获取继承链
            const inheritanceChain = getInheritanceChain(material);

            content.innerHTML = `
                <div class="config-form">
                    <div class="config-form__header">
                        <h4>🔗 四级继承链: 业务方 → 供应商 → 品类 → 物料</h4>
                        <p>物料配置具有最高优先级，可以覆盖所有下级配置。选择继承或自定义每个参数。</p>
                    </div>
                    <form id="configForm">
                        ${Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                            const paramConfig = resolveParameterConfig(material, param.key, inheritanceChain);
                            const isCustom = paramConfig.source === 'MATERIAL';

                            return `
                                <div class="config-field">
                                    <div class="config-field__header">
                                        <label class="config-field__label">
                                            ${param.icon} ${param.label}
                                        </label>
                                        <div class="config-field__inheritance">
                                            <span class="inheritance-info">
                                                🔗 当前来源: ${paramConfig.sourceLevel} - ${paramConfig.displayValue}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="config-field__chain">
                                        ${renderParameterChain(param.key, inheritanceChain)}
                                    </div>
                                    <div class="config-field__control">
                                        <label class="config-field__mode">
                                            <input type="radio" name="${param.key}_mode" value="inherit" ${!isCustom ? 'checked' : ''}>
                                            继承配置 (${paramConfig.sourceLevel})
                                        </label>
                                        <label class="config-field__mode">
                                            <input type="radio" name="${param.key}_mode" value="custom" ${isCustom ? 'checked' : ''}>
                                            自定义配置
                                        </label>
                                        <div class="config-field__input ${!isCustom ? 'config-field__input--disabled' : ''}">
                                            ${renderConfigInput(param, material.config?.[param.key] || paramConfig.value)}
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                        <div class="config-form__actions">
                            <button type="button" class="btn btn--secondary" onclick="closeConfigModal()">取消</button>
                            <button type="submit" class="btn btn--primary">保存配置</button>
                        </div>
                    </form>
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // 绑定模式切换事件
            bindConfigModeEvents();
        }

        function renderConfigInput(param, value) {
            if (param.type === 'boolean') {
                return `
                    <label><input type="radio" name="${param.key}" value="true" ${value === true ? 'checked' : ''}> 开启</label>
                    <label><input type="radio" name="${param.key}" value="false" ${value === false ? 'checked' : ''}> 关闭</label>
                `;
            } else if (param.type === 'select') {
                return `
                    <select name="${param.key}" class="input">
                        ${param.options.map(opt => `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
                    </select>
                `;
            }
            return `<input type="text" name="${param.key}" class="input" value="${value || ''}">`;
        }

        function bindConfigModeEvents() {
            const modeInputs = document.querySelectorAll('input[name$="_mode"]');
            modeInputs.forEach(input => {
                input.addEventListener('change', (e) => {
                    const paramKey = e.target.name.replace('_mode', '');
                    const inputContainer = e.target.closest('.config-field').querySelector('.config-field__input');
                    const isCustom = e.target.value === 'custom';

                    if (isCustom) {
                        inputContainer.classList.remove('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = false);
                    } else {
                        inputContainer.classList.add('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = true);
                    }
                });
            });
        }

        function closeConfigModal() {
            const modal = document.getElementById('configModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showInheritanceModal(material) {
            const modal = document.getElementById('inheritanceModal');
            const content = document.getElementById('inheritanceContent');

            const inheritanceChain = getInheritanceChain(material);

            content.innerHTML = `
                <div class="inheritance-visualization">
                    <div class="inheritance-header">
                        <h4>📦 ${material.materialName} - 配置继承链可视化</h4>
                        <p>展示四级配置的继承关系和参数来源</p>
                    </div>

                    <div class="inheritance-flow">
                        ${renderInheritanceFlow(inheritanceChain)}
                    </div>

                    <div class="inheritance-details">
                        <h5>📋 详细配置对比</h5>
                        <div class="inheritance-table">
                            ${renderInheritanceTable(inheritanceChain)}
                        </div>
                    </div>

                    <div class="inheritance-actions">
                        <button class="btn btn--secondary" onclick="closeInheritanceModal()">关闭</button>
                        <button class="btn btn--primary" onclick="configMaterial('${material.id}')">编辑配置</button>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function renderInheritanceFlow(chain) {
            const levels = [
                { key: 'factory', label: '业务方级别', icon: '🏭', data: chain.factory, priority: 'Level 4' },
                { key: 'supplier', label: '供应商级别', icon: '🏢', data: chain.supplier, priority: 'Level 3' },
                { key: 'category', label: '品类级别', icon: '📂', data: chain.category, priority: 'Level 2' },
                { key: 'material', label: '物料级别', icon: '📦', data: chain.material, priority: 'Level 1' }
            ];

            return levels.map((level, index) => {
                const hasData = level.data;
                const hasConfig = hasData && level.data.config && Object.keys(level.data.config).length > 0;
                const isActive = hasConfig;

                let html = `
                    <div class="inheritance-level ${isActive ? 'inheritance-level--active' : 'inheritance-level--inactive'}">
                        <div class="inheritance-level__icon">${level.icon}</div>
                        <div class="inheritance-level__content">
                            <div class="inheritance-level__label">${level.label}</div>
                            <div class="inheritance-level__priority">${level.priority}</div>
                            ${hasData ? `
                                <div class="inheritance-level__name">${level.data.factoryName || level.data.supplierName || level.data.categoryName || level.data.materialName}</div>
                                <div class="inheritance-level__code">${level.data.factoryCode || level.data.supplierCode || level.data.categoryCode || level.data.materialCode}</div>
                            ` : `
                                <div class="inheritance-level__empty">未关联</div>
                            `}
                            <div class="inheritance-level__status">
                                ${hasConfig ? '✅ 已配置' : '❌ 未配置'}
                            </div>
                        </div>
                    </div>
                `;

                if (index < levels.length - 1) {
                    html += '<div class="inheritance-arrow">→</div>';
                }

                return html;
            }).join('');
        }

        function renderInheritanceTable(chain) {
            const levels = [
                { key: 'factory', label: '业务方', data: chain.factory },
                { key: 'supplier', label: '供应商', data: chain.supplier },
                { key: 'category', label: '品类', data: chain.category },
                { key: 'material', label: '物料', data: chain.material }
            ];

            const headers = ['参数', ...levels.map(l => l.label)];

            const rows = Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                const cells = [param.label];

                levels.forEach(level => {
                    const config = level.data?.config;
                    const value = config?.[param.key];
                    let displayValue = '-';
                    let className = 'inheritance-cell--empty';

                    if (value !== null && value !== undefined) {
                        if (param.type === 'boolean') {
                            displayValue = value ? '✅ 开启' : '❌ 关闭';
                        } else if (param.type === 'select') {
                            const option = param.options.find(opt => opt.value === value);
                            displayValue = option ? option.label : value;
                        } else {
                            displayValue = value;
                        }
                        className = 'inheritance-cell--configured';
                    }

                    cells.push(`<span class="${className}">${displayValue}</span>`);
                });

                return `<tr><td>${cells[0]}</td>${cells.slice(1).map(cell => `<td>${cell}</td>`).join('')}</tr>`;
            });

            return `
                <table class="inheritance-table__table">
                    <thead>
                        <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
                        ${rows.join('')}
                    </tbody>
                </table>
            `;
        }

        function closeInheritanceModal() {
            const modal = document.getElementById('inheritanceModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showConfigAnalysisModal() {
            const modal = document.getElementById('analysisModal');
            const content = document.getElementById('analysisContent');

            // 模拟分析数据
            const analysisData = {
                totalMaterials: currentData.length,
                configuredMaterials: currentData.filter(m => m.status === 'CONFIGURED').length,
                inheritedMaterials: currentData.filter(m => m.status === 'INHERITED').length,
                unconfiguredMaterials: currentData.filter(m => m.status === 'NOT_CONFIGURED').length,
                configSources: {
                    MATERIAL: currentData.filter(m => m.configSource === 'MATERIAL').length,
                    CATEGORY: currentData.filter(m => m.configSource === 'CATEGORY').length,
                    SUPPLIER: currentData.filter(m => m.configSource === 'SUPPLIER').length,
                    FACTORY: currentData.filter(m => m.configSource === 'FACTORY').length
                }
            };

            content.innerHTML = `
                <div class="config-analysis">
                    <div class="analysis-summary">
                        <h4>📊 配置统计概览</h4>
                        <div class="analysis-stats">
                            <div class="analysis-stat">
                                <div class="analysis-stat__value">${analysisData.totalMaterials}</div>
                                <div class="analysis-stat__label">总物料数</div>
                            </div>
                            <div class="analysis-stat">
                                <div class="analysis-stat__value">${analysisData.configuredMaterials}</div>
                                <div class="analysis-stat__label">已配置</div>
                            </div>
                            <div class="analysis-stat">
                                <div class="analysis-stat__value">${analysisData.inheritedMaterials}</div>
                                <div class="analysis-stat__label">继承配置</div>
                            </div>
                            <div class="analysis-stat">
                                <div class="analysis-stat__value">${analysisData.unconfiguredMaterials}</div>
                                <div class="analysis-stat__label">未配置</div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-sources">
                        <h5>🔗 配置来源分布</h5>
                        <div class="source-chart">
                            <div class="source-item">
                                <span class="source-icon">📦</span>
                                <span class="source-label">物料自定义</span>
                                <span class="source-value">${analysisData.configSources.MATERIAL}</span>
                                <div class="source-bar">
                                    <div class="source-bar__fill" style="width: ${(analysisData.configSources.MATERIAL / analysisData.totalMaterials * 100)}%"></div>
                                </div>
                            </div>
                            <div class="source-item">
                                <span class="source-icon">📂</span>
                                <span class="source-label">品类继承</span>
                                <span class="source-value">${analysisData.configSources.CATEGORY}</span>
                                <div class="source-bar">
                                    <div class="source-bar__fill" style="width: ${(analysisData.configSources.CATEGORY / analysisData.totalMaterials * 100)}%"></div>
                                </div>
                            </div>
                            <div class="source-item">
                                <span class="source-icon">🏢</span>
                                <span class="source-label">供应商继承</span>
                                <span class="source-value">${analysisData.configSources.SUPPLIER}</span>
                                <div class="source-bar">
                                    <div class="source-bar__fill" style="width: ${(analysisData.configSources.SUPPLIER / analysisData.totalMaterials * 100)}%"></div>
                                </div>
                            </div>
                            <div class="source-item">
                                <span class="source-icon">🏭</span>
                                <span class="source-label">业务方继承</span>
                                <span class="source-value">${analysisData.configSources.FACTORY}</span>
                                <div class="source-bar">
                                    <div class="source-bar__fill" style="width: ${(analysisData.configSources.FACTORY / analysisData.totalMaterials * 100)}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-actions">
                        <button class="btn btn--secondary" onclick="closeAnalysisModal()">关闭</button>
                        <button class="btn btn--primary" onclick="handleExport()">导出报告</button>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeAnalysisModal() {
            const modal = document.getElementById('analysisModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showInheritanceViewModal() {
            Utils.showMessage('继承视图功能开发中...', 'info');
        }

        function handleMaterialSubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            Utils.showMessage('物料信息保存成功', 'success');
            closeMaterialModal();
            loadData();
        }

        function handleBatchInherit() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要设置继承的物料', 'warning');
                return;
            }

            Utils.confirm(`确定要让选中的 ${selectedRows.length} 个物料继承配置吗？`, '批量继承确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已设置 ${selectedRows.length} 个物料继承配置`, 'success');
                    loadData();
                }
            });
        }

        function handleBatchDelete() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要删除的物料', 'warning');
                return;
            }

            Utils.confirm(`确定要删除选中的 ${selectedRows.length} 个物料吗？`, '批量删除确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已删除 ${selectedRows.length} 个物料`, 'success');
                    loadData();
                }
            });
        }

        function handleExport() {
            Utils.showMessage('导出功能开发中...', 'info');
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMaterialModal();
                closeConfigModal();
                closeInheritanceModal();
                closeAnalysisModal();
            }
        });
    </script>

    <style>
        /* 物料配置页面特定样式 */
        .inheritance-chain {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid var(--border-secondary);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .inheritance-chain__header h3 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .inheritance-chain__header p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .inheritance-chain__flow {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .inheritance-level {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--spacing-md);
            background-color: var(--bg-primary);
            border: 2px solid var(--border-secondary);
            border-radius: var(--border-radius-md);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .inheritance-level--factory {
            border-color: #6c757d;
        }

        .inheritance-level--supplier {
            border-color: #17a2b8;
        }

        .inheritance-level--category {
            border-color: #ffc107;
        }

        .inheritance-level--material {
            border-color: var(--color-primary);
        }

        .inheritance-level--active {
            background-color: var(--color-primary-light);
            border-color: var(--color-primary);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }

        .inheritance-level__icon {
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-xs);
        }

        .inheritance-level__label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            text-align: center;
        }

        .inheritance-level__priority {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            background-color: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: var(--border-radius-sm);
        }

        .inheritance-arrow {
            font-size: var(--font-size-lg);
            color: var(--color-primary);
            font-weight: bold;
        }

        .config-param__chain {
            margin: var(--spacing-sm) 0;
            padding: var(--spacing-sm);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-sm);
            border-left: 3px solid var(--color-primary);
        }

        .chain-level {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-xs) 0;
            font-size: var(--font-size-sm);
        }

        .chain-level--active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .chain-level--inactive {
            color: var(--text-tertiary);
        }

        .chain-level__icon {
            width: 20px;
            text-align: center;
        }

        .chain-level__label {
            min-width: 60px;
        }

        .chain-level__value {
            flex: 1;
            text-align: right;
        }

        .inheritance-visualization {
            max-height: 80vh;
            overflow-y: auto;
        }

        .inheritance-header {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
        }

        .inheritance-header h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
        }

        .inheritance-header p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .inheritance-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .inheritance-level__content {
            text-align: center;
        }

        .inheritance-level__name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .inheritance-level__code {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .inheritance-level__empty {
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
            font-style: italic;
        }

        .inheritance-level__status {
            font-size: var(--font-size-xs);
            padding: 2px 6px;
            border-radius: var(--border-radius-sm);
            background-color: var(--bg-tertiary);
        }

        .inheritance-level--active .inheritance-level__status {
            background-color: var(--color-success-light);
            color: var(--color-success);
        }

        .inheritance-level--inactive .inheritance-level__status {
            background-color: var(--bg-secondary);
            color: var(--text-tertiary);
        }

        .inheritance-details {
            margin-bottom: var(--spacing-lg);
        }

        .inheritance-details h5 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
        }

        .inheritance-table__table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-sm);
        }

        .inheritance-table__table th,
        .inheritance-table__table td {
            padding: var(--spacing-sm);
            border: 1px solid var(--border-secondary);
            text-align: left;
        }

        .inheritance-table__table th {
            background-color: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .inheritance-cell--configured {
            color: var(--color-success);
            font-weight: 500;
        }

        .inheritance-cell--empty {
            color: var(--text-tertiary);
            font-style: italic;
        }

        .inheritance-actions {
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-secondary);
            text-align: right;
        }

        .inheritance-actions .btn {
            margin-left: var(--spacing-sm);
        }

        .config-analysis {
            max-height: 70vh;
            overflow-y: auto;
        }

        .analysis-summary {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
        }

        .analysis-summary h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
        }

        .analysis-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
        }

        .analysis-stat {
            text-align: center;
            padding: var(--spacing-md);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-md);
        }

        .analysis-stat__value {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
        }

        .analysis-stat__label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .analysis-sources {
            margin-bottom: var(--spacing-lg);
        }

        .analysis-sources h5 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
        }

        .source-chart {
            display: grid;
            gap: var(--spacing-sm);
        }

        .source-item {
            display: grid;
            grid-template-columns: auto 1fr auto auto;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-sm);
        }

        .source-icon {
            font-size: var(--font-size-lg);
        }

        .source-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .source-value {
            font-weight: 600;
            color: var(--color-primary);
            min-width: 30px;
            text-align: right;
        }

        .source-bar {
            width: 100px;
            height: 8px;
            background-color: var(--bg-tertiary);
            border-radius: var(--border-radius-sm);
            overflow: hidden;
        }

        .source-bar__fill {
            height: 100%;
            background-color: var(--color-primary);
            transition: width 0.3s ease;
        }

        .analysis-actions {
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-secondary);
            text-align: right;
        }

        .analysis-actions .btn {
            margin-left: var(--spacing-sm);
        }

        @media (max-width: 767px) {
            .inheritance-chain__flow {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .inheritance-arrow {
                transform: rotate(90deg);
            }

            .inheritance-level {
                min-width: 200px;
            }

            .inheritance-flow {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .inheritance-table__table {
                font-size: var(--font-size-xs);
            }

            .inheritance-table__table th,
            .inheritance-table__table td {
                padding: var(--spacing-xs);
            }

            .analysis-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .source-item {
                grid-template-columns: auto 1fr;
                gap: var(--spacing-xs);
            }

            .source-value,
            .source-bar {
                grid-column: 1 / -1;
                margin-top: var(--spacing-xs);
            }
        }
    </style>
</body>
</html>