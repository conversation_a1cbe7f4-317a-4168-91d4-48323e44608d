<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品类配置管理 - 采购协同配置中心</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="app">

        <!-- 主要内容区域 -->
        <main class="main">
            <div class="main__container">
                <!-- 面包屑导航 -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb__list">
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">🏠 首页</a>
                        </li>
                        <li class="breadcrumb__item">
                            <a href="index.html" class="breadcrumb__link">配置管理</a>
                        </li>
                        <li class="breadcrumb__item breadcrumb__item--current">
                            <span class="breadcrumb__text">品类配置</span>
                        </li>
                    </ol>
                </nav>

                <!-- 页面标题 -->
                <section class="page-header">
                    <h2 class="page-header__title">📂 品类配置管理</h2>
                    <p class="page-header__description">
                        管理品类级别的配置参数，支持树形结构和继承关系。配置优先级：物料级别 > 品类级别 > 供应商级别 > 业务方级别。
                    </p>
                    <div class="page-header__meta">
                        <span class="page-header__level">📊 配置级别: Level 2 - 品类级别</span>
                    </div>
                </section>

                <!-- 搜索筛选区域 -->
                <section class="search" id="searchContainer">
                    <div class="search__basic">
                        <div class="search__field">
                            <label class="search__label">🔍 关键词:</label>
                            <input type="text" name="keyword" class="input search__input" placeholder="品类代码、品类名称">
                        </div>
                        <div class="search__field">
                            <label class="search__label">🏢 业务方:</label>
                            <select name="businessEntity" class="input search__input">
                                <option value="">全部业务方</option>
                                <option value="上海电气风电集团">上海电气风电集团</option>
                            </select>
                        </div>
                        <div class="search__field">
                            <button class="btn btn--primary search__btn">🔍 搜索</button>
                        </div>
                    </div>
                    
                    <div class="search__advanced" style="display: none;">
                        <div class="search__advanced-grid">
                            <div class="search__field">
                                <label class="search__label">📂 品类代码:</label>
                                <input type="text" name="categoryCode" class="input search__input" placeholder="C001">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📊 品类级别:</label>
                                <select name="level" class="input search__input">
                                    <option value="">全部级别</option>
                                    <option value="1">一级品类</option>
                                    <option value="2">二级品类</option>
                                    <option value="3">三级品类</option>
                                </select>
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 创建时间:</label>
                                <input type="date" name="createTimeStart" class="input search__input">
                            </div>
                            <div class="search__field">
                                <label class="search__label">📅 至:</label>
                                <input type="date" name="createTimeEnd" class="input search__input">
                            </div>
                        </div>
                        <div class="search__actions">
                            <button class="btn btn--primary">🔍 高级搜索</button>
                            <button class="btn btn--secondary">📋 保存条件</button>
                            <button class="btn btn--secondary">📂 加载预设</button>
                        </div>
                    </div>
                </section>

                <!-- 工具栏 -->
                <section class="toolbar">
                    <div class="toolbar__left">
                        <button class="btn btn--primary" id="addCategoryBtn">➕ 新增品类</button>
                        <button class="btn btn--secondary" id="batchConfigBtn" disabled>🔧 批量配置</button>
                        <button class="btn btn--danger" id="batchDeleteBtn" disabled>🗑️ 批量删除</button>
                    </div>
                    <div class="toolbar__right">
                        <button class="btn btn--secondary" id="exportBtn">📥 导入</button>
                        <button class="btn btn--secondary" id="exportBtn">📤 导出</button>
                    </div>
                </section>

                <!-- 表格视图 -->
                <section id="tableView" class="table-section">
                    <div id="tableContainer" class="table-container">
                        <!-- 表格内容将通过JavaScript动态生成 -->
                    </div>
                    
                    <!-- 移动端卡片视图 -->
                    <div id="mobileCards" class="mobile-cards d-none">
                        <!-- 移动端卡片将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 分页 -->
                <section id="paginationContainer" class="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer__container">
                <p class="footer__text">
                    © 2024 上海电气风电集团有限公司 - 采购协同配置中心 v1.0
                </p>
            </div>
        </footer>
    </div>

    <!-- 新增/编辑品类模态框 -->
    <div id="categoryModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 600px;">
            <div class="modal__header">
                <h3 class="modal__title" id="modalTitle">新增品类</h3>
                <button class="modal__close" onclick="closeCategoryModal()">×</button>
            </div>
            <div class="modal__body">
                <form id="categoryForm">
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="form-field">
                            <label class="form-label">📂 品类代码 *</label>
                            <input type="text" name="categoryCode" class="input" required placeholder="C001">
                        </div>
                        <div class="form-field">
                            <label class="form-label">📂 品类名称 *</label>
                            <input type="text" name="categoryName" class="input" required placeholder="风电设备">
                        </div>
                    </div>
                    <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="form-field">
                            <label class="form-label">🏢 业务方 *</label>
                            <select name="businessEntity" class="input" required>
                                <option value="">请选择业务方</option>
                                <option value="上海电气风电集团">上海电气风电集团</option>
                            </select>
                        </div>
                        <div class="form-field" style="display: none;">
                            <label class="form-label">📂 父品类</label>
                            <select name="parentCategory" class="input">
                                <option value="">无（一级品类）</option>
                                <!-- 父品类选项将通过JavaScript动态生成 -->
                            </select>
                        </div>
                    </div>
                    <div class="form-actions" style="text-align: right; border-top: 1px solid #f0f0f0; padding-top: 16px;">
                        <button type="button" class="btn btn--secondary" onclick="closeCategoryModal()" style="margin-right: 8px;">取消</button>
                        <button type="submit" class="btn btn--primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 配置参数模态框 -->
    <div id="configModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 800px;">
            <div class="modal__header">
                <h3 class="modal__title" id="configModalTitle">品类配置</h3>
                <button class="modal__close" onclick="closeConfigModal()">×</button>
            </div>
            <div class="modal__body">
                <div id="configContent">
                    <!-- 配置内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 影响范围模态框 -->
    <div id="impactModal" class="modal" style="display: none;">
        <div class="modal__overlay"></div>
        <div class="modal__content" style="max-width: 900px;">
            <div class="modal__header">
                <h3 class="modal__title">📊 配置影响范围分析</h3>
                <button class="modal__close" onclick="closeImpactModal()">×</button>
            </div>
            <div class="modal__body">
                <div id="impactContent">
                    <!-- 影响范围内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 (移动端) -->
    <button class="fab d-none" id="mobileFab">+</button>

    <script src="js/common.js"></script>
    <script src="js/data.js"></script>
    <script src="js/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCategoryConfigPage();
        });

        // 全局变量
        let currentData = [];
        let currentFilters = {};
        let currentPage = 1;
        let currentPageSize = CONFIG.DEFAULT_PAGE_SIZE;
        let tableComponent = null;
        let searchComponent = null;
        let paginationComponent = null;
        let accordionComponent = null;

        // 初始化页面
        function initCategoryConfigPage() {
            initComponents();
            loadData();
            bindEvents();
            setupResponsive();
        }

        // 初始化组件
        function initComponents() {
            // 搜索组件
            searchComponent = new SearchComponent(document.getElementById('searchContainer'), {
                onSearch: handleSearch,
                onReset: handleReset
            });

            // 分页组件
            paginationComponent = new PaginationComponent(document.getElementById('paginationContainer'), {
                onChange: handlePageChange,
                onShowSizeChange: handlePageSizeChange
            });
        }

        // 加载数据
        function loadData() {
            const filters = {
                ...currentFilters,
                page: currentPage,
                pageSize: currentPageSize
            };

            const result = DataManager.getCategories(filters);
            currentData = result.data;

            renderTableView();
            paginationComponent.update(result.total, result.page);
            updateToolbarState();
        }







        // 渲染表格视图
        function renderTableView() {
            const columns = [
                {
                    title: '品类代码',
                    dataIndex: 'categoryCode',
                    render: (value, record) => `
                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 8px;">📂</span>
                            <strong>${value}</strong>
                        </div>
                    `
                },
                {
                    title: '品类名称',
                    dataIndex: 'categoryName'
                },
                {
                    title: '业务方',
                    dataIndex: 'businessEntity'
                },
                {
                    title: '更新时间',
                    dataIndex: 'updateTime',
                    className: 'table__cell--secondary'
                },
                {
                    title: '操作',
                    dataIndex: 'actions',
                    render: (value, record) => `
                        <div class="table__actions">
                            <button class="btn btn--small btn--primary" onclick="editCategory('${record.id}')">编辑</button>
                            <button class="btn btn--small btn--secondary" onclick="configCategory('${record.id}')">配置</button>
                            <button class="btn btn--small btn--danger" onclick="deleteCategory('${record.id}')">删除</button>
                        </div>
                    `
                }
            ];

            // 显示分页
            document.getElementById('paginationContainer').style.display = 'block';

            // 检查是否为移动端
            if (window.innerWidth < 768) {
                renderMobileCards();
            } else {
                renderDesktopTable(columns);
            }
        }

        // 渲染桌面端表格
        function renderDesktopTable(columns) {
            document.getElementById('mobileCards').classList.add('d-none');
            document.getElementById('tableContainer').classList.remove('d-none');

            tableComponent = new TableComponent(document.getElementById('tableContainer'), {
                columns: columns,
                data: currentData,
                selectable: true,
                expandable: true,
                expandedRowRender: renderConfigParams,
                onSelectionChange: handleSelectionChange,
                onExpand: handleRowExpand
            });

            // 初始化手风琴组件
            setTimeout(() => {
                const accordionContainer = document.querySelector('.table-container');
                if (accordionContainer) {
                    accordionComponent = new AccordionComponent(accordionContainer);
                }
            }, 100);
        }

        // 渲染移动端卡片
        function renderMobileCards() {
            document.getElementById('tableContainer').classList.add('d-none');
            const mobileContainer = document.getElementById('mobileCards');
            mobileContainer.classList.remove('d-none');

            mobileContainer.innerHTML = currentData.map(record => {
                let statusIcon = '';
                switch (record.status) {
                    case 'CONFIGURED': statusIcon = '🔧'; break;
                    case 'INHERITED': statusIcon = '🔗'; break;
                    default: statusIcon = '⚠️';
                }

                return `
                    <div class="mobile-card" data-id="${record.id}">
                        <div class="mobile-card__header">
                            <div class="mobile-card__title">
                                📂 ${record.categoryCode} - ${record.categoryName}
                            </div>
                            <div class="mobile-card__actions">
                                <button class="btn btn--small btn--primary" onclick="editCategory('${record.id}')">编辑</button>
                                <button class="btn btn--small btn--secondary" onclick="configCategory('${record.id}')">配置</button>
                            </div>
                        </div>
                        <div class="mobile-card__content">
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">级别</div>
                                <div class="mobile-card__value">L${record.level}</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">父品类</div>
                                <div class="mobile-card__value">${record.parentCategory || '-'}</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">配置状态</div>
                                <div class="mobile-card__value">
                                    <span class="status ${record.status === 'CONFIGURED' ? 'status--success' : record.status === 'INHERITED' ? 'status--info' : 'status--warning'}">
                                        ${statusIcon} ${record.configStatus}
                                    </span>
                                </div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">统计信息</div>
                                <div class="mobile-card__value">👥 ${record.childrenCount} 子品类 | 📦 ${record.materialCount} 物料</div>
                            </div>
                            <div class="mobile-card__field">
                                <div class="mobile-card__label">更新时间</div>
                                <div class="mobile-card__value">${record.updateTime}</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            // 显示浮动操作按钮
            document.getElementById('mobileFab').classList.remove('d-none');
        }

        // 渲染配置参数
        function renderConfigParams(record) {
            if (!record.config && record.status !== 'INHERITED') {
                return `
                    <div class="config-params">
                        <div class="config-params__empty">
                            <p>🔧 该品类尚未配置参数</p>
                            <button class="btn btn--primary" onclick="configCategory('${record.id}')">立即配置</button>
                            ${record.parentCategory ? `
                                <button class="btn btn--secondary" onclick="inheritFromParent('${record.id}')">继承父品类配置</button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            // 获取父品类配置作为继承基础
            const parentConfig = record.parentCategory ? 
                CATEGORY_DATA.find(c => c.categoryCode === record.parentCategory)?.config || {} : {};
            
            const params = Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                let value, source, displayValue;
                
                if (record.config && record.config[param.key] !== null && record.config[param.key] !== undefined) {
                    // 自定义配置
                    value = record.config[param.key];
                    source = 'CUSTOM';
                } else {
                    // 继承配置
                    value = parentConfig[param.key];
                    source = 'INHERITED';
                }
                
                if (param.type === 'boolean') {
                    displayValue = value ? '✅ 开启' : '❌ 关闭';
                } else if (param.type === 'select') {
                    const option = param.options.find(opt => opt.value === value);
                    displayValue = option ? option.label : value;
                } else {
                    displayValue = value || '';
                }

                const sourceIcon = source === 'CUSTOM' ? '🔧' : '🔗';
                const sourceText = source === 'CUSTOM' ? '自定义' : '继承';
                const sourceClass = source === 'CUSTOM' ? 'config-source--custom' : 'config-source--inherited';

                return `
                    <div class="config-param">
                        <div class="config-param__header">
                            <span class="config-param__icon">${param.icon}</span>
                            <span class="config-param__label">${param.label}</span>
                            <span class="config-source ${sourceClass}">${sourceIcon} ${sourceText}</span>
                        </div>
                        <div class="config-param__value">${displayValue}</div>
                    </div>
                `;
            }).join('');

            return `
                <div class="config-params">
                    <div class="config-params__header">
                        <h4>📋 配置参数</h4>
                        <div class="config-params__actions">
                            <button class="btn btn--small btn--primary" onclick="configCategory('${record.id}')">编辑配置</button>
                            <button class="btn btn--small btn--secondary" onclick="copyConfig('${record.id}')">复制配置</button>
                            <button class="btn btn--small btn--info" onclick="viewImpact('${record.id}')">影响范围</button>
                        </div>
                    </div>
                    <div class="config-params__grid">
                        ${params}
                    </div>
                </div>
            `;
        }

        // 事件绑定
        function bindEvents() {


            // 新增品类
            document.getElementById('addCategoryBtn').addEventListener('click', () => {
                showCategoryModal();
            });



            // 批量操作
            document.getElementById('batchConfigBtn').addEventListener('click', () => {
                showBatchConfigModal();
            });

            document.getElementById('batchDeleteBtn').addEventListener('click', () => {
                handleBatchDelete();
            });

            // 展开/收起
            document.getElementById('expandAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.expandAll();
                }
            });

            document.getElementById('collapseAllBtn').addEventListener('click', () => {
                if (accordionComponent) {
                    accordionComponent.collapseAll();
                }
            });



            // 其他操作
            document.getElementById('exportBtn').addEventListener('click', handleExport);
            document.getElementById('refreshBtn').addEventListener('click', loadData);

            // 移动端浮动按钮
            document.getElementById('mobileFab').addEventListener('click', () => {
                showCategoryModal();
            });

            // 品类表单提交
            document.getElementById('categoryForm').addEventListener('submit', handleCategorySubmit);
        }





        // 搜索处理
        function handleSearch(filters) {
            currentFilters = filters;
            currentPage = 1;
            loadData();
        }

        // 重置处理
        function handleReset() {
            currentFilters = {};
            currentPage = 1;
            loadData();
        }

        // 分页处理
        function handlePageChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 页面大小改变处理
        function handlePageSizeChange(page, pageSize) {
            currentPage = page;
            currentPageSize = pageSize;
            loadData();
        }

        // 选择改变处理
        function handleSelectionChange(selectedRows) {
            updateToolbarState(selectedRows.length > 0);
        }

        // 行展开处理
        function handleRowExpand(record, expanded) {
            console.log('Row expand:', record.categoryCode, expanded);
        }

        // 更新工具栏状态
        function updateToolbarState(hasSelection = false) {
            document.getElementById('batchConfigBtn').disabled = !hasSelection;
            document.getElementById('batchDeleteBtn').disabled = !hasSelection;
        }

        // 响应式设置
        function setupResponsive() {
            window.addEventListener('resize', Utils.debounce(() => {
                renderTableView();
            }, 300));
        }

        // 品类操作函数
        function editCategory(id) {
            const category = currentData.find(item => item.id === id);
            if (category) {
                showCategoryModal(category);
            }
        }

        function configCategory(id) {
            const category = currentData.find(item => item.id === id);
            if (category) {
                showConfigModal(category);
            }
        }

        function viewImpact(id) {
            const category = currentData.find(item => item.id === id);
            if (category) {
                showImpactModal(category);
            }
        }

        function addSubCategory(parentId) {
            const parent = currentData.find(item => item.id === parentId);
            if (parent) {
                showCategoryModal(null, parent);
            }
        }

        function deleteCategory(id) {
            const category = currentData.find(item => item.id === id);
            if (category) {
                Utils.confirm(`确定要删除品类 "${category.categoryName}" 吗？\n注意：删除后其子品类和物料的配置可能受到影响。`, '删除确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已删除品类: ${category.categoryName}`, 'success');
                        loadData();
                    }
                });
            }
        }

        function inheritFromParent(id) {
            const category = currentData.find(item => item.id === id);
            if (category) {
                Utils.confirm(`确定要让品类 "${category.categoryName}" 继承父品类配置吗？`, '继承确认').then(confirmed => {
                    if (confirmed) {
                        Utils.showMessage(`已设置为继承父品类配置`, 'success');
                        loadData();
                    }
                });
            }
        }

        // 模态框操作
        function showCategoryModal(category = null, parent = null) {
            const modal = document.getElementById('categoryModal');
            const title = document.getElementById('modalTitle');
            const form = document.getElementById('categoryForm');
            
            // 更新父品类选项
            updateParentCategoryOptions(category?.id);
            
            if (category) {
                title.textContent = '编辑品类';
                form.categoryCode.value = category.categoryCode;
                form.categoryName.value = category.categoryName;
                form.businessEntity.value = category.businessEntity;
                form.parentCategory.value = category.parentCategory || '';
                form.categoryCode.readOnly = true;
            } else {
                title.textContent = parent ? `新增子品类 (父品类: ${parent.categoryName})` : '新增品类';
                form.reset();
                if (parent) {
                    form.parentCategory.value = parent.categoryCode;
                    form.businessEntity.value = parent.businessEntity;
                }
                form.categoryCode.readOnly = false;
            }
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function updateParentCategoryOptions(excludeId = null) {
            const select = document.querySelector('[name="parentCategory"]');
            const options = ['<option value="">无（一级品类）</option>'];
            
            currentData.forEach(category => {
                if (category.id !== excludeId) {
                    options.push(`<option value="${category.categoryCode}">${category.categoryCode} - ${category.categoryName}</option>`);
                }
            });
            
            select.innerHTML = options.join('');
        }

        function closeCategoryModal() {
            const modal = document.getElementById('categoryModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showConfigModal(category) {
            const modal = document.getElementById('configModal');
            const title = document.getElementById('configModalTitle');
            const content = document.getElementById('configContent');
            
            title.textContent = `${category.categoryName} - 配置参数`;
            
            // 获取父品类配置作为继承基础
            const parentConfig = category.parentCategory ? 
                CATEGORY_DATA.find(c => c.categoryCode === category.parentCategory)?.config || {} : {};
            
            content.innerHTML = `
                <div class="config-form">
                    <form id="configForm">
                        ${Object.entries(CONFIG.CONFIG_PARAMS).map(([key, param]) => {
                            const currentValue = category.config?.[param.key];
                            const inheritedValue = parentConfig[param.key];
                            const isCustom = currentValue !== null && currentValue !== undefined;
                            
                            return `
                                <div class="config-field">
                                    <div class="config-field__header">
                                        <label class="config-field__label">
                                            ${param.icon} ${param.label}
                                        </label>
                                        ${category.parentCategory ? `
                                            <div class="config-field__inheritance">
                                                <span class="inheritance-info">
                                                    🔗 父品类配置: ${renderConfigValue(param, inheritedValue)}
                                                </span>
                                            </div>
                                        ` : ''}
                                    </div>
                                    <div class="config-field__control">
                                        ${category.parentCategory ? `
                                            <label class="config-field__mode">
                                                <input type="radio" name="${param.key}_mode" value="inherit" ${!isCustom ? 'checked' : ''}> 
                                                继承父品类配置
                                            </label>
                                        ` : ''}
                                        <label class="config-field__mode">
                                            <input type="radio" name="${param.key}_mode" value="custom" ${isCustom || !category.parentCategory ? 'checked' : ''}> 
                                            自定义配置
                                        </label>
                                        <div class="config-field__input ${!isCustom && category.parentCategory ? 'config-field__input--disabled' : ''}">
                                            ${renderConfigInput(param, currentValue || inheritedValue)}
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                        <div class="config-form__actions">
                            <button type="button" class="btn btn--secondary" onclick="closeConfigModal()">取消</button>
                            <button type="submit" class="btn btn--primary">保存配置</button>
                        </div>
                    </form>
                </div>
            `;
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // 绑定模式切换事件
            bindConfigModeEvents();
        }

        function renderConfigValue(param, value) {
            if (param.type === 'boolean') {
                return value ? '✅ 开启' : '❌ 关闭';
            } else if (param.type === 'select') {
                const option = param.options.find(opt => opt.value === value);
                return option ? option.label : value || '-';
            }
            return value || '-';
        }

        function renderConfigInput(param, value) {
            if (param.type === 'boolean') {
                return `
                    <label><input type="radio" name="${param.key}" value="true" ${value === true ? 'checked' : ''}> 开启</label>
                    <label><input type="radio" name="${param.key}" value="false" ${value === false ? 'checked' : ''}> 关闭</label>
                `;
            } else if (param.type === 'select') {
                return `
                    <select name="${param.key}" class="input">
                        ${param.options.map(opt => `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
                    </select>
                `;
            }
            return `<input type="text" name="${param.key}" class="input" value="${value || ''}">`;
        }

        function bindConfigModeEvents() {
            const modeInputs = document.querySelectorAll('input[name$="_mode"]');
            modeInputs.forEach(input => {
                input.addEventListener('change', (e) => {
                    const paramKey = e.target.name.replace('_mode', '');
                    const inputContainer = e.target.closest('.config-field').querySelector('.config-field__input');
                    const isCustom = e.target.value === 'custom';
                    
                    if (isCustom) {
                        inputContainer.classList.remove('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = false);
                    } else {
                        inputContainer.classList.add('config-field__input--disabled');
                        inputContainer.querySelectorAll('input, select').forEach(el => el.disabled = true);
                    }
                });
            });
        }

        function closeConfigModal() {
            const modal = document.getElementById('configModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showImpactModal(category) {
            const modal = document.getElementById('impactModal');
            const content = document.getElementById('impactContent');
            
            // 模拟影响范围数据
            const impactData = {
                subCategories: CATEGORY_DATA.filter(c => c.parentCategory === category.categoryCode),
                materials: MATERIAL_DATA.filter(m => m.categoryCode === category.categoryCode),
                affectedSuppliers: 15,
                affectedFactories: 3
            };
            
            content.innerHTML = `
                <div class="impact-analysis">
                    <div class="impact-summary">
                        <h4>📊 ${category.categoryName} 配置影响范围</h4>
                        <div class="impact-stats">
                            <div class="impact-stat">
                                <div class="impact-stat__value">${impactData.subCategories.length}</div>
                                <div class="impact-stat__label">子品类</div>
                            </div>
                            <div class="impact-stat">
                                <div class="impact-stat__value">${impactData.materials.length}</div>
                                <div class="impact-stat__label">物料</div>
                            </div>
                            <div class="impact-stat">
                                <div class="impact-stat__value">${impactData.affectedSuppliers}</div>
                                <div class="impact-stat__label">供应商</div>
                            </div>
                            <div class="impact-stat">
                                <div class="impact-stat__value">${impactData.affectedFactories}</div>
                                <div class="impact-stat__label">业务方</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="impact-details">
                        <div class="impact-section">
                            <h5>📂 受影响的子品类 (${impactData.subCategories.length})</h5>
                            <div class="impact-list">
                                ${impactData.subCategories.map(sub => `
                                    <div class="impact-item">
                                        <span class="impact-item__name">${sub.categoryCode} - ${sub.categoryName}</span>
                                        <span class="impact-item__status status ${sub.status === 'CONFIGURED' ? 'status--success' : 'status--info'}">
                                            ${sub.status === 'CONFIGURED' ? '🔧 已配置' : '🔗 继承配置'}
                                        </span>
                                    </div>
                                `).join('')}
                                ${impactData.subCategories.length === 0 ? '<p class="impact-empty">无子品类</p>' : ''}
                            </div>
                        </div>
                        
                        <div class="impact-section">
                            <h5>📦 受影响的物料 (${impactData.materials.length})</h5>
                            <div class="impact-list">
                                ${impactData.materials.map(material => `
                                    <div class="impact-item">
                                        <span class="impact-item__name">${material.materialCode} - ${material.materialName}</span>
                                        <span class="impact-item__status status ${material.status === 'CONFIGURED' ? 'status--success' : 'status--info'}">
                                            ${material.status === 'CONFIGURED' ? '🔧 已配置' : '🔗 继承配置'}
                                        </span>
                                    </div>
                                `).join('')}
                                ${impactData.materials.length === 0 ? '<p class="impact-empty">无物料</p>' : ''}
                            </div>
                        </div>
                    </div>
                    
                    <div class="impact-actions">
                        <button class="btn btn--secondary" onclick="closeImpactModal()">关闭</button>
                        <button class="btn btn--primary" onclick="configCategory('${category.id}')">编辑配置</button>
                    </div>
                </div>
            `;
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeImpactModal() {
            const modal = document.getElementById('impactModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function handleCategorySubmit(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            Utils.showMessage('品类信息保存成功', 'success');
            closeCategoryModal();
            loadData();
        }

        function handleBatchDelete() {
            const selectedRows = tableComponent ? tableComponent.getSelectedRows() : [];
            if (selectedRows.length === 0) {
                Utils.showMessage('请先选择要删除的品类', 'warning');
                return;
            }

            Utils.confirm(`确定要删除选中的 ${selectedRows.length} 个品类吗？\n注意：删除后其子品类和物料的配置可能受到影响。`, '批量删除确认').then(confirmed => {
                if (confirmed) {
                    Utils.showMessage(`已删除 ${selectedRows.length} 个品类`, 'success');
                    loadData();
                }
            });
        }

        function handleExport() {
            Utils.showMessage('导出功能开发中...', 'info');
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCategoryModal();
                closeConfigModal();
                closeImpactModal();
            }
        });
    </script>

    <style>
        /* 品类配置页面特定样式 */



        .category-stats {
            display: flex;
            gap: var(--spacing-sm);
            font-size: var(--font-size-sm);
            color: var(--text-tertiary);
        }

        .impact-analysis {
            max-height: 70vh;
            overflow-y: auto;
        }

        .impact-summary {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
        }

        .impact-summary h4 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
        }

        .impact-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
        }

        .impact-stat {
            text-align: center;
            padding: var(--spacing-md);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-md);
        }

        .impact-stat__value {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
        }

        .impact-stat__label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .impact-details {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .impact-section h5 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
        }

        .impact-list {
            display: grid;
            gap: var(--spacing-sm);
            max-height: 200px;
            overflow-y: auto;
        }

        .impact-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-sm);
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius-sm);
        }

        .impact-item__name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .impact-item__status {
            font-size: var(--font-size-xs);
        }

        .impact-empty {
            text-align: center;
            color: var(--text-tertiary);
            font-style: italic;
            padding: var(--spacing-lg);
        }

        .impact-actions {
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-secondary);
            text-align: right;
        }

        .impact-actions .btn {
            margin-left: var(--spacing-sm);
        }

        @media (max-width: 767px) {
            
            .impact-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .impact-details {
                gap: var(--spacing-md);
            }
        }
    </style>
</body>
</html>
