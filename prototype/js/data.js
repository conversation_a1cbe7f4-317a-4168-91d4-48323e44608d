// 模拟数据 - 采购协同配置中心

// 业务方数据
const FACTORY_DATA = [
    {
        id: 'F001',
        factoryCode: 'F001',
        factoryName: '上海电气风电集团',
        companyName: '1502｜上海电气风电集团',
        status: 'CONFIGURED',
        configStatus: '已配置',
        createTime: '2024-01-10 09:00:00',
        updateTime: '2024-01-15 14:30:00',
        config: {
            purchaseRequest: true,
            orderSend: true,
            orderConfirm: false,
            collaborationMode: 'WORK_PROGRESS',
            invoiceMethod: 'SAP',
            advanceInspection: true,
            qualityAssurance: true,
            shippingApproval: false
        }
    },
    {
        id: 'F002',
        factoryCode: 'F002',
        factoryName: '上海电气风电东台',
        companyName: '1503｜上海电气风电集团',
        status: 'CONFIGURED',
        configStatus: '已配置',
        createTime: '2024-01-08 10:15:00',
        updateTime: '2024-01-14 16:20:00',
        config: {
            purchaseRequest: false,
            orderSend: true,
            orderConfirm: true,
            collaborationMode: 'MATERIAL_COMMITMENT',
            invoiceMethod: 'CQT',
            advanceInspection: false,
            qualityAssurance: true,
            shippingApproval: true
        }
    },
    {
        id: 'F003',
        factoryCode: '15A2',
        factoryName: '风电驱动链事业部',
        companyName: '1502｜上海电气风电集团',
        status: 'NOT_CONFIGURED',
        configStatus: '未配置',
        createTime: '2024-01-12 11:30:00',
        updateTime: '2024-01-12 11:30:00',
        config: null
    },
];

// 供应商数据
const SUPPLIER_DATA = [
    {
        id: 'S001',
        factoryCode: 'F001',
        factoryName: '上海业务方',
        supplierCode: 'S001',
        supplierName: '风机叶片供应商A',
        status: 'CONFIGURED',
        configStatus: '已配置',
        inheritanceChain: ['F001'],
        createTime: '2024-01-11 10:00:00',
        updateTime: '2024-01-15 15:30:00',
        config: {
            purchaseRequest: true,
            orderSend: false, // 自定义配置
            orderConfirm: null, // 继承业务方配置
            collaborationMode: 'MATERIAL_COMMITMENT', // 自定义配置
            invoiceMethod: null, // 继承业务方配置
            advanceInspection: null, // 继承业务方配置
            qualityAssurance: false, // 自定义配置
            shippingApproval: null // 继承业务方配置
        },
        configSource: {
            purchaseRequest: 'CUSTOM',
            orderSend: 'CUSTOM',
            orderConfirm: 'INHERITED',
            collaborationMode: 'CUSTOM',
            invoiceMethod: 'INHERITED',
            advanceInspection: 'INHERITED',
            qualityAssurance: 'CUSTOM',
            shippingApproval: 'INHERITED'
        }
    },
    {
        id: 'S002',
        factoryCode: 'F001',
        factoryName: '上海业务方',
        supplierCode: 'S002',
        supplierName: '齿轮箱供应商B',
        status: 'INHERITED',
        configStatus: '继承配置',
        inheritanceChain: ['F001'],
        createTime: '2024-01-12 09:30:00',
        updateTime: '2024-01-12 09:30:00',
        config: null, // 完全继承业务方配置
        configSource: {
            purchaseRequest: 'INHERITED',
            orderSend: 'INHERITED',
            orderConfirm: 'INHERITED',
            collaborationMode: 'INHERITED',
            invoiceMethod: 'INHERITED',
            advanceInspection: 'INHERITED',
            qualityAssurance: 'INHERITED',
            shippingApproval: 'INHERITED'
        }
    },
    {
        id: 'S003',
        factoryCode: 'F002',
        factoryName: '北京业务方',
        supplierCode: 'S003',
        supplierName: '发电机供应商C',
        status: 'CONFIGURED',
        configStatus: '已配置',
        inheritanceChain: ['F002'],
        createTime: '2024-01-09 14:15:00',
        updateTime: '2024-01-14 17:45:00',
        config: {
            purchaseRequest: null, // 继承业务方配置
            orderSend: true, // 自定义配置
            orderConfirm: false, // 自定义配置
            collaborationMode: null, // 继承业务方配置
            invoiceMethod: 'SAP', // 自定义配置
            advanceInspection: true, // 自定义配置
            qualityAssurance: null, // 继承业务方配置
            shippingApproval: null // 继承业务方配置
        },
        configSource: {
            purchaseRequest: 'INHERITED',
            orderSend: 'CUSTOM',
            orderConfirm: 'CUSTOM',
            collaborationMode: 'INHERITED',
            invoiceMethod: 'CUSTOM',
            advanceInspection: 'CUSTOM',
            qualityAssurance: 'INHERITED',
            shippingApproval: 'INHERITED'
        }
    }
];

// 品类数据
const CATEGORY_DATA = [
    {
        id: 'C001',
        businessEntity: '上海电气风电集团',
        categoryCode: 'C001',
        categoryName: '风电设备',
        parentCategory: null,
        level: 1,
        status: 'CONFIGURED',
        configStatus: '已配置',
        childrenCount: 3,
        materialCount: 45,
        createTime: '2024-01-08 08:00:00',
        updateTime: '2024-01-15 12:00:00',
        config: {
            purchaseRequest: true,
            orderSend: true,
            orderConfirm: true,
            collaborationMode: 'WORK_PROGRESS',
            invoiceMethod: 'SAP',
            advanceInspection: false,
            qualityAssurance: true,
            shippingApproval: true
        }
    },
    {
        id: 'C002',
        businessEntity: '上海电气风电集团',
        categoryCode: 'C002',
        categoryName: '风机叶片',
        parentCategory: 'C001',
        level: 2,
        status: 'CONFIGURED',
        configStatus: '已配置',
        childrenCount: 0,
        materialCount: 12,
        createTime: '2024-01-08 08:30:00',
        updateTime: '2024-01-14 10:30:00',
        config: {
            purchaseRequest: null, // 继承父品类
            orderSend: false, // 自定义配置
            orderConfirm: null, // 继承父品类
            collaborationMode: 'MATERIAL_COMMITMENT', // 自定义配置
            invoiceMethod: null, // 继承父品类
            advanceInspection: true, // 自定义配置
            qualityAssurance: null, // 继承父品类
            shippingApproval: null // 继承父品类
        }
    },
    {
        id: 'C003',
        businessEntity: '上海电气风电集团',
        categoryCode: 'C003',
        categoryName: '齿轮箱',
        parentCategory: 'C001',
        level: 2,
        status: 'INHERITED',
        configStatus: '继承配置',
        childrenCount: 0,
        materialCount: 18,
        createTime: '2024-01-08 09:00:00',
        updateTime: '2024-01-08 09:00:00',
        config: null // 完全继承父品类配置
    },
    {
        id: 'C004',
        businessEntity: '上海电气风电集团',
        categoryCode: 'C004',
        categoryName: '发电机',
        parentCategory: 'C001',
        level: 2,
        status: 'NOT_CONFIGURED',
        configStatus: '未配置',
        childrenCount: 0,
        materialCount: 15,
        createTime: '2024-01-10 11:00:00',
        updateTime: '2024-01-10 11:00:00',
        config: null
    },
    {
        id: 'C005',
        businessEntity: '上海电气风电集团',
        categoryCode: 'C005',
        categoryName: '电气设备',
        parentCategory: null,
        level: 1,
        status: 'NOT_CONFIGURED',
        configStatus: '未配置',
        childrenCount: 2,
        materialCount: 28,
        createTime: '2024-01-12 14:00:00',
        updateTime: '2024-01-12 14:00:00',
        config: null
    }
];

// 物料数据
const MATERIAL_DATA = [
    {
        id: 'M001',
        materialCode: 'M001',
        materialName: '2MW风机叶片-主叶片',
        categoryCode: 'C002',
        categoryName: '风机叶片',
        status: 'CONFIGURED',
        configStatus: '已配置',
        inheritanceChain: ['C002', 'C001'],
        createTime: '2024-01-09 10:00:00',
        updateTime: '2024-01-15 16:00:00',
        config: {
            purchaseRequest: false, // 自定义配置
            orderSend: null, // 继承品类配置
            orderConfirm: true, // 自定义配置
            collaborationMode: null, // 继承品类配置
            invoiceMethod: 'CQT', // 自定义配置
            advanceInspection: null, // 继承品类配置
            qualityAssurance: false, // 自定义配置
            shippingApproval: null // 继承品类配置
        },
        configSource: {
            purchaseRequest: 'CUSTOM',
            orderSend: 'INHERITED',
            orderConfirm: 'CUSTOM',
            collaborationMode: 'INHERITED',
            invoiceMethod: 'CUSTOM',
            advanceInspection: 'INHERITED',
            qualityAssurance: 'CUSTOM',
            shippingApproval: 'INHERITED'
        }
    },
    {
        id: 'M002',
        materialCode: 'M002',
        materialName: '3MW风机叶片-主叶片',
        categoryCode: 'C002',
        categoryName: '风机叶片',
        status: 'INHERITED',
        configStatus: '继承配置',
        inheritanceChain: ['C002', 'C001'],
        createTime: '2024-01-10 11:30:00',
        updateTime: '2024-01-10 11:30:00',
        config: null, // 完全继承品类配置
        configSource: {
            purchaseRequest: 'INHERITED',
            orderSend: 'INHERITED',
            orderConfirm: 'INHERITED',
            collaborationMode: 'INHERITED',
            invoiceMethod: 'INHERITED',
            advanceInspection: 'INHERITED',
            qualityAssurance: 'INHERITED',
            shippingApproval: 'INHERITED'
        }
    },
    {
        id: 'M003',
        materialCode: 'M003',
        materialName: '齿轮箱-主减速器',
        categoryCode: 'C003',
        categoryName: '齿轮箱',
        status: 'CONFIGURED',
        configStatus: '已配置',
        inheritanceChain: ['C003', 'C001'],
        createTime: '2024-01-11 09:15:00',
        updateTime: '2024-01-14 14:45:00',
        config: {
            purchaseRequest: null, // 继承品类配置
            orderSend: null, // 继承品类配置
            orderConfirm: false, // 自定义配置
            collaborationMode: 'WORK_PROGRESS', // 自定义配置
            invoiceMethod: null, // 继承品类配置
            advanceInspection: null, // 继承品类配置
            qualityAssurance: null, // 继承品类配置
            shippingApproval: false // 自定义配置
        },
        configSource: {
            purchaseRequest: 'INHERITED',
            orderSend: 'INHERITED',
            orderConfirm: 'CUSTOM',
            collaborationMode: 'CUSTOM',
            invoiceMethod: 'INHERITED',
            advanceInspection: 'INHERITED',
            qualityAssurance: 'INHERITED',
            shippingApproval: 'CUSTOM'
        }
    }
];

// 数据管理器
const DataManager = {
    // 获取业务方数据
    getFactories(filters = {}) {
        let data = [...FACTORY_DATA];
        
        // 应用筛选
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            data = data.filter(item => 
                item.factoryCode.toLowerCase().includes(keyword) ||
                item.factoryName.toLowerCase().includes(keyword) ||
                item.companyName.toLowerCase().includes(keyword)
            );
        }
        
        if (filters.status) {
            data = data.filter(item => item.status === filters.status);
        }
        
        // 分页
        const total = data.length;
        const pageSize = filters.pageSize || CONFIG.DEFAULT_PAGE_SIZE;
        const page = filters.page || 1;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        return {
            data: data.slice(start, end),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    },

    // 获取供应商数据
    getSuppliers(filters = {}) {
        let data = [...SUPPLIER_DATA];
        
        // 应用筛选
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            data = data.filter(item => 
                item.supplierCode.toLowerCase().includes(keyword) ||
                item.supplierName.toLowerCase().includes(keyword) ||
                item.factoryName.toLowerCase().includes(keyword)
            );
        }
        
        if (filters.factoryCode) {
            data = data.filter(item => item.factoryCode === filters.factoryCode);
        }
        
        if (filters.status) {
            data = data.filter(item => item.status === filters.status);
        }
        
        // 分页
        const total = data.length;
        const pageSize = filters.pageSize || CONFIG.DEFAULT_PAGE_SIZE;
        const page = filters.page || 1;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        return {
            data: data.slice(start, end),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    },

    // 获取品类数据
    getCategories(filters = {}) {
        let data = [...CATEGORY_DATA];
        
        // 应用筛选
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            data = data.filter(item => 
                item.categoryCode.toLowerCase().includes(keyword) ||
                item.categoryName.toLowerCase().includes(keyword)
            );
        }
        
        if (filters.status) {
            data = data.filter(item => item.status === filters.status);
        }
        
        // 分页
        const total = data.length;
        const pageSize = filters.pageSize || CONFIG.DEFAULT_PAGE_SIZE;
        const page = filters.page || 1;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        return {
            data: data.slice(start, end),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    },

    // 获取物料数据
    getMaterials(filters = {}) {
        let data = [...MATERIAL_DATA];
        
        // 应用筛选
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            data = data.filter(item => 
                item.materialCode.toLowerCase().includes(keyword) ||
                item.materialName.toLowerCase().includes(keyword) ||
                item.categoryName.toLowerCase().includes(keyword)
            );
        }
        
        if (filters.categoryCode) {
            data = data.filter(item => item.categoryCode === filters.categoryCode);
        }
        
        if (filters.status) {
            data = data.filter(item => item.status === filters.status);
        }
        
        // 分页
        const total = data.length;
        const pageSize = filters.pageSize || CONFIG.DEFAULT_PAGE_SIZE;
        const page = filters.page || 1;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        return {
            data: data.slice(start, end),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    },

    // 保存配置
    saveConfig(type, id, config) {
        let dataArray;
        switch (type) {
            case 'factory':
                dataArray = FACTORY_DATA;
                break;
            case 'supplier':
                dataArray = SUPPLIER_DATA;
                break;
            case 'category':
                dataArray = CATEGORY_DATA;
                break;
            case 'material':
                dataArray = MATERIAL_DATA;
                break;
            default:
                throw new Error('Invalid type');
        }
        
        const item = dataArray.find(item => item.id === id);
        if (item) {
            item.config = { ...config };
            item.updateTime = Utils.formatDate(new Date());
            item.status = 'CONFIGURED';
            item.configStatus = '已配置';
            
            // 保存到本地存储
            Storage.set(`${type}_data`, dataArray);
            
            return true;
        }
        return false;
    },

    // 删除配置
    deleteConfig(type, id) {
        let dataArray;
        switch (type) {
            case 'factory':
                dataArray = FACTORY_DATA;
                break;
            case 'supplier':
                dataArray = SUPPLIER_DATA;
                break;
            case 'category':
                dataArray = CATEGORY_DATA;
                break;
            case 'material':
                dataArray = MATERIAL_DATA;
                break;
            default:
                throw new Error('Invalid type');
        }
        
        const item = dataArray.find(item => item.id === id);
        if (item) {
            item.config = null;
            item.updateTime = Utils.formatDate(new Date());
            item.status = 'NOT_CONFIGURED';
            item.configStatus = '未配置';
            
            // 保存到本地存储
            Storage.set(`${type}_data`, dataArray);
            
            return true;
        }
        return false;
    }
};

// 导出到全局
window.FACTORY_DATA = FACTORY_DATA;
window.SUPPLIER_DATA = SUPPLIER_DATA;
window.CATEGORY_DATA = CATEGORY_DATA;
window.MATERIAL_DATA = MATERIAL_DATA;
window.DataManager = DataManager;
