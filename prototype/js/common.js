// 通用功能 - 采购协同配置中心

// 全局配置
const CONFIG = {
    // API配置
    API_BASE_URL: '/api/v1',
    
    // 分页配置
    DEFAULT_PAGE_SIZE: 20,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
    
    // 缓存配置
    CACHE_DURATION: 5 * 60 * 1000, // 5分钟
    
    // 动画配置
    ANIMATION_DURATION: 300,
    
    // 配置参数定义
    CONFIG_PARAMS: {
        PURCHASE_REQUEST: { key: 'purchaseRequest', label: '采购申请', type: 'boolean', icon: '📋' },
        ORDER_SEND: { key: 'orderSend', label: '订单发送', type: 'boolean', icon: '📤' },
        ORDER_CONFIRM: { key: 'orderConfirm', label: '订单确认', type: 'boolean', icon: '📋' },
        COLLABORATION_MODE: { 
            key: 'collaborationMode', 
            label: '协同模式', 
            type: 'select', 
            icon: '🔄',
            options: [
                { value: 'WORK_PROGRESS', label: '报工进度' },
                { value: 'MATERIAL_COMMITMENT', label: '物料承诺' }
            ]
        },
        INVOICE_METHOD: { 
            key: 'invoiceMethod', 
            label: '发票方式', 
            type: 'select', 
            icon: '💰',
            options: [
                { value: 'SAP', label: 'SAP' },
                { value: 'CQT', label: 'CQT' },
                { value: 'ALL', label: 'ALL' }
            ]
        },
        ADVANCE_INSPECTION: { key: 'advanceInspection', label: '提前检验', type: 'boolean', icon: '✅' },
        QUALITY_ASSURANCE: { key: 'qualityAssurance', label: '质保资料', type: 'boolean', icon: '📄' },
        SHIPPING_APPROVAL: { key: 'shippingApproval', label: '装运审批', type: 'boolean', icon: '🚚' }
    }
};

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 生成UUID
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    // 深拷贝
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // 获取查询参数
    getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    // 设置查询参数
    setQueryParam(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    },

    // 移除查询参数
    removeQueryParam(name) {
        const url = new URL(window.location);
        url.searchParams.delete(name);
        window.history.pushState({}, '', url);
    },

    // 显示消息提示
    showMessage(message, type = 'info', duration = 3000) {
        const messageContainer = this.getOrCreateMessageContainer();
        const messageElement = this.createMessageElement(message, type);
        
        messageContainer.appendChild(messageElement);
        
        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, duration);
        
        return messageElement;
    },

    // 获取或创建消息容器
    getOrCreateMessageContainer() {
        let container = document.getElementById('message-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'message-container';
            container.className = 'message-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        return container;
    },

    // 创建消息元素
    createMessageElement(message, type) {
        const element = document.createElement('div');
        element.className = `message message--${type}`;
        element.style.cssText = `
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            pointer-events: auto;
            animation: messageSlideIn 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        const colors = {
            success: { border: '#52c41a', background: '#f6ffed', color: '#52c41a' },
            warning: { border: '#faad14', background: '#fffbe6', color: '#faad14' },
            error: { border: '#ff4d4f', background: '#fff2f0', color: '#ff4d4f' },
            info: { border: '#1890ff', background: '#e6f7ff', color: '#1890ff' }
        };
        
        if (colors[type]) {
            element.style.borderColor = colors[type].border;
            element.style.backgroundColor = colors[type].background;
            element.style.color = colors[type].color;
        }
        
        element.textContent = message;
        
        // 添加关闭按钮
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 8px;
            cursor: pointer;
            font-weight: bold;
        `;
        closeBtn.onclick = () => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        };
        element.appendChild(closeBtn);
        
        return element;
    },

    // 确认对话框
    confirm(message, title = '确认') {
        return new Promise((resolve) => {
            const modal = this.createConfirmModal(message, title, resolve);
            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';
        });
    },

    // 创建确认模态框
    createConfirmModal(message, title, resolve) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        
        modal.innerHTML = `
            <div class="modal__overlay"></div>
            <div class="modal__content" style="max-width: 400px;">
                <div class="modal__header">
                    <h3 class="modal__title">${title}</h3>
                </div>
                <div class="modal__body">
                    <p style="margin: 0; line-height: 1.6;">${message}</p>
                </div>
                <div class="modal__footer" style="padding: 16px 24px; border-top: 1px solid #f0f0f0; text-align: right;">
                    <button class="btn btn--secondary" data-action="cancel" style="margin-right: 8px;">取消</button>
                    <button class="btn btn--primary" data-action="confirm">确认</button>
                </div>
            </div>
        `;
        
        // 事件处理
        modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal__overlay') || e.target.dataset.action === 'cancel') {
                this.closeConfirmModal(modal, resolve, false);
            } else if (e.target.dataset.action === 'confirm') {
                this.closeConfirmModal(modal, resolve, true);
            }
        });
        
        // ESC键处理
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeConfirmModal(modal, resolve, false);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
        
        return modal;
    },

    // 关闭确认模态框
    closeConfirmModal(modal, resolve, result) {
        document.body.style.overflow = 'auto';
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
        resolve(result);
    }
};

// 本地存储管理
const Storage = {
    // 设置数据
    set(key, value, expiry = null) {
        const data = {
            value: value,
            expiry: expiry ? Date.now() + expiry : null
        };
        localStorage.setItem(key, JSON.stringify(data));
    },

    // 获取数据
    get(key) {
        const item = localStorage.getItem(key);
        if (!item) return null;
        
        try {
            const data = JSON.parse(item);
            if (data.expiry && Date.now() > data.expiry) {
                localStorage.removeItem(key);
                return null;
            }
            return data.value;
        } catch (e) {
            localStorage.removeItem(key);
            return null;
        }
    },

    // 移除数据
    remove(key) {
        localStorage.removeItem(key);
    },

    // 清空所有数据
    clear() {
        localStorage.clear();
    },

    // 获取所有键
    keys() {
        return Object.keys(localStorage);
    }
};

// 事件总线
const EventBus = {
    events: {},

    // 订阅事件
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    },

    // 取消订阅
    off(event, callback) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(cb => cb !== callback);
    },

    // 触发事件
    emit(event, data) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
    }
};

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
`;
document.head.appendChild(style);

// 导出到全局
window.CONFIG = CONFIG;
window.Utils = Utils;
window.Storage = Storage;
window.EventBus = EventBus;
