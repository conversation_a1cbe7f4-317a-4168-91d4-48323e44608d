// 组件逻辑 - 采购协同配置中心

// 手风琴组件
class AccordionComponent {
    constructor(container) {
        this.container = container;
        this.activeItems = new Set();
        this.init();
    }

    init() {
        this.container.addEventListener('click', this.handleClick.bind(this));
    }

    handleClick(event) {
        const header = event.target.closest('.accordion__header');
        if (!header) return;

        const item = header.closest('.accordion__item');
        const content = item.querySelector('.accordion__content');
        const icon = header.querySelector('.accordion__icon');
        
        if (this.activeItems.has(item)) {
            this.collapse(item, content, header, icon);
        } else {
            this.expand(item, content, header, icon);
        }
    }

    expand(item, content, header, icon) {
        this.activeItems.add(item);
        header.classList.add('accordion__header--active');
        content.classList.add('accordion__content--active');
        
        // 计算内容高度
        const body = content.querySelector('.accordion__body');
        const height = body.scrollHeight;
        content.style.maxHeight = height + 'px';
        
        // 图标动画
        if (icon) {
            icon.style.transform = 'rotate(180deg)';
        }
        
        // 触发展开事件
        EventBus.emit('accordion:expand', { item, content });
    }

    collapse(item, content, header, icon) {
        this.activeItems.delete(item);
        header.classList.remove('accordion__header--active');
        content.classList.remove('accordion__content--active');
        content.style.maxHeight = '0';
        
        // 图标动画
        if (icon) {
            icon.style.transform = 'rotate(0deg)';
        }
        
        // 触发收起事件
        EventBus.emit('accordion:collapse', { item, content });
    }

    expandAll() {
        const items = this.container.querySelectorAll('.accordion__item');
        items.forEach(item => {
            const header = item.querySelector('.accordion__header');
            const content = item.querySelector('.accordion__content');
            const icon = header.querySelector('.accordion__icon');
            
            if (!this.activeItems.has(item)) {
                this.expand(item, content, header, icon);
            }
        });
    }

    collapseAll() {
        const items = this.container.querySelectorAll('.accordion__item');
        items.forEach(item => {
            const header = item.querySelector('.accordion__header');
            const content = item.querySelector('.accordion__content');
            const icon = header.querySelector('.accordion__icon');
            
            if (this.activeItems.has(item)) {
                this.collapse(item, content, header, icon);
            }
        });
    }
}

// 搜索组件
class SearchComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            debounceDelay: 300,
            onSearch: () => {},
            onReset: () => {},
            ...options
        };
        this.filters = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupAdvancedToggle();
    }

    bindEvents() {
        // 基础搜索
        const basicInputs = this.container.querySelectorAll('.search__basic input, .search__basic select');
        basicInputs.forEach(input => {
            const debouncedSearch = Utils.debounce(() => {
                this.updateFilters();
                this.options.onSearch(this.filters);
            }, this.options.debounceDelay);
            
            input.addEventListener('input', debouncedSearch);
            input.addEventListener('change', debouncedSearch);
        });

        // 高级搜索
        const advancedInputs = this.container.querySelectorAll('.search__advanced input, .search__advanced select');
        advancedInputs.forEach(input => {
            const debouncedSearch = Utils.debounce(() => {
                this.updateFilters();
                this.options.onSearch(this.filters);
            }, this.options.debounceDelay);
            
            input.addEventListener('input', debouncedSearch);
            input.addEventListener('change', debouncedSearch);
        });

        // 搜索按钮
        const searchBtn = this.container.querySelector('.search__btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.updateFilters();
                this.options.onSearch(this.filters);
            });
        }

        // 重置按钮
        const resetBtn = this.container.querySelector('.search__reset');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.reset();
            });
        }
    }

    setupAdvancedToggle() {
        const toggle = this.container.querySelector('.search__advanced-toggle');
        const advanced = this.container.querySelector('.search__advanced');
        
        if (toggle && advanced) {
            toggle.addEventListener('click', () => {
                const isVisible = advanced.style.display !== 'none';
                advanced.style.display = isVisible ? 'none' : 'block';
                toggle.textContent = isVisible ? '展开高级搜索 ▼' : '收起高级搜索 ▲';
            });
        }
    }

    updateFilters() {
        this.filters = {};
        
        // 收集所有输入值
        const inputs = this.container.querySelectorAll('input, select');
        inputs.forEach(input => {
            if (input.name && input.value) {
                this.filters[input.name] = input.value;
            }
        });
    }

    reset() {
        // 清空所有输入
        const inputs = this.container.querySelectorAll('input, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        this.filters = {};
        this.options.onReset();
        this.options.onSearch(this.filters);
    }

    getFilters() {
        return { ...this.filters };
    }

    setFilters(filters) {
        this.filters = { ...filters };
        
        // 更新UI
        Object.keys(filters).forEach(key => {
            const input = this.container.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = !!filters[key];
                } else {
                    input.value = filters[key] || '';
                }
            }
        });
    }
}

// 分页组件
class PaginationComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            pageSize: CONFIG.DEFAULT_PAGE_SIZE,
            pageSizeOptions: CONFIG.PAGE_SIZE_OPTIONS,
            showSizeChanger: true,
            showQuickJumper: false,
            onChange: () => {},
            onShowSizeChange: () => {},
            ...options
        };
        this.currentPage = 1;
        this.total = 0;
        this.pageSize = this.options.pageSize;
        this.init();
    }

    init() {
        this.render();
    }

    render() {
        const totalPages = Math.ceil(this.total / this.pageSize);
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, this.total);

        this.container.innerHTML = `
            <div class="pagination__info">
                显示 ${start}-${end} 条，共 ${this.total} 条
            </div>
            <div class="pagination__controls">
                ${this.options.showSizeChanger ? this.renderSizeChanger() : ''}
                <button class="pagination__button" ${this.currentPage <= 1 ? 'disabled' : ''} data-action="prev">
                    上一页
                </button>
                ${this.renderPageNumbers(totalPages)}
                <button class="pagination__button" ${this.currentPage >= totalPages ? 'disabled' : ''} data-action="next">
                    下一页
                </button>
            </div>
        `;

        this.bindEvents();
    }

    renderSizeChanger() {
        return `
            <div class="pagination__size">
                每页
                <select class="pagination__size-select">
                    ${this.options.pageSizeOptions.map(size => 
                        `<option value="${size}" ${size === this.pageSize ? 'selected' : ''}>${size}</option>`
                    ).join('')}
                </select>
                条
            </div>
        `;
    }

    renderPageNumbers(totalPages) {
        const pages = [];
        const maxVisible = 7;
        
        if (totalPages <= maxVisible) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            const start = Math.max(1, this.currentPage - 3);
            const end = Math.min(totalPages, start + maxVisible - 1);
            
            if (start > 1) {
                pages.push(1);
                if (start > 2) pages.push('...');
            }
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            if (end < totalPages) {
                if (end < totalPages - 1) pages.push('...');
                pages.push(totalPages);
            }
        }

        return pages.map(page => {
            if (page === '...') {
                return '<span class="pagination__ellipsis">...</span>';
            }
            return `
                <button class="pagination__button ${page === this.currentPage ? 'pagination__button--active' : ''}" 
                        data-action="page" data-page="${page}">
                    ${page}
                </button>
            `;
        }).join('');
    }

    bindEvents() {
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            
            if (action === 'prev' && this.currentPage > 1) {
                this.goToPage(this.currentPage - 1);
            } else if (action === 'next') {
                const totalPages = Math.ceil(this.total / this.pageSize);
                if (this.currentPage < totalPages) {
                    this.goToPage(this.currentPage + 1);
                }
            } else if (action === 'page') {
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            }
        });

        // 页面大小改变
        const sizeSelect = this.container.querySelector('.pagination__size-select');
        if (sizeSelect) {
            sizeSelect.addEventListener('change', (e) => {
                const newSize = parseInt(e.target.value);
                this.changePageSize(newSize);
            });
        }
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.total / this.pageSize);
        if (page >= 1 && page <= totalPages && page !== this.currentPage) {
            this.currentPage = page;
            this.render();
            this.options.onChange(page, this.pageSize);
        }
    }

    changePageSize(size) {
        if (size !== this.pageSize) {
            this.pageSize = size;
            this.currentPage = 1;
            this.render();
            this.options.onShowSizeChange(this.currentPage, size);
        }
    }

    update(total, page = 1) {
        this.total = total;
        this.currentPage = page;
        this.render();
    }
}

// 表格组件
class TableComponent {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            columns: [],
            data: [],
            rowKey: 'id',
            selectable: false,
            expandable: false,
            onRowClick: () => {},
            onSelectionChange: () => {},
            onExpand: () => {},
            ...options
        };
        this.selectedRows = new Set();
        this.expandedRows = new Set();
        this.init();
    }

    init() {
        this.render();
    }

    render() {
        const { columns, data, selectable, expandable } = this.options;
        
        this.container.innerHTML = `
            <div class="table-container">
                <table class="table">
                    <thead class="table__header">
                        <tr>
                            ${selectable ? '<th class="table__header-cell"><input type="checkbox" class="select-all"></th>' : ''}
                            ${expandable ? '<th class="table__header-cell"></th>' : ''}
                            ${columns.map(col => `
                                <th class="table__header-cell ${col.className || ''}">${col.title}</th>
                            `).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => this.renderRow(row)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.bindEvents();
    }

    renderRow(row) {
        const { columns, selectable, expandable, rowKey } = this.options;
        const key = row[rowKey];
        const isSelected = this.selectedRows.has(key);
        const isExpanded = this.expandedRows.has(key);
        
        let html = `
            <tr class="table__row ${isSelected ? 'table__row--selected' : ''}" data-key="${key}">
                ${selectable ? `<td class="table__cell"><input type="checkbox" class="row-select" ${isSelected ? 'checked' : ''}></td>` : ''}
                ${expandable ? `<td class="table__cell"><button class="expand-btn">${isExpanded ? '▼' : '▶'}</button></td>` : ''}
                ${columns.map(col => `
                    <td class="table__cell ${col.className || ''}">
                        ${col.render ? col.render(row[col.dataIndex], row) : (row[col.dataIndex] || '')}
                    </td>
                `).join('')}
            </tr>
        `;

        // 展开行
        if (expandable && isExpanded) {
            html += `
                <tr class="table__row table__row--expanded" data-key="${key}-expanded">
                    <td colspan="${columns.length + (selectable ? 1 : 0) + (expandable ? 1 : 0)}" class="table__cell">
                        <div class="table__expanded-content">
                            ${this.options.expandedRowRender ? this.options.expandedRowRender(row) : ''}
                        </div>
                    </td>
                </tr>
            `;
        }

        return html;
    }

    bindEvents() {
        // 全选
        const selectAll = this.container.querySelector('.select-all');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                const checked = e.target.checked;
                this.selectAll(checked);
            });
        }

        // 行选择
        this.container.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-select')) {
                const row = e.target.closest('.table__row');
                const key = row.dataset.key;
                const checked = e.target.checked;
                
                if (checked) {
                    this.selectedRows.add(key);
                    row.classList.add('table__row--selected');
                } else {
                    this.selectedRows.delete(key);
                    row.classList.remove('table__row--selected');
                }
                
                this.updateSelectAllState();
                this.options.onSelectionChange(Array.from(this.selectedRows));
            }
        });

        // 行点击
        this.container.addEventListener('click', (e) => {
            const row = e.target.closest('.table__row:not(.table__row--expanded)');
            if (row && !e.target.closest('input, button')) {
                const key = row.dataset.key;
                const rowData = this.options.data.find(item => item[this.options.rowKey] === key);
                this.options.onRowClick(rowData, row);
            }
        });

        // 展开/收起
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('expand-btn')) {
                const row = e.target.closest('.table__row');
                const key = row.dataset.key;
                const isExpanded = this.expandedRows.has(key);
                
                if (isExpanded) {
                    this.collapseRow(key);
                } else {
                    this.expandRow(key);
                }
            }
        });
    }

    selectAll(checked) {
        const checkboxes = this.container.querySelectorAll('.row-select');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const row = checkbox.closest('.table__row');
            const key = row.dataset.key;
            
            if (checked) {
                this.selectedRows.add(key);
                row.classList.add('table__row--selected');
            } else {
                this.selectedRows.delete(key);
                row.classList.remove('table__row--selected');
            }
        });
        
        this.options.onSelectionChange(Array.from(this.selectedRows));
    }

    updateSelectAllState() {
        const selectAll = this.container.querySelector('.select-all');
        if (selectAll) {
            const totalRows = this.options.data.length;
            const selectedCount = this.selectedRows.size;
            
            selectAll.checked = selectedCount === totalRows && totalRows > 0;
            selectAll.indeterminate = selectedCount > 0 && selectedCount < totalRows;
        }
    }

    expandRow(key) {
        this.expandedRows.add(key);
        this.render();
        
        const rowData = this.options.data.find(item => item[this.options.rowKey] === key);
        this.options.onExpand(rowData, true);
    }

    collapseRow(key) {
        this.expandedRows.delete(key);
        this.render();
        
        const rowData = this.options.data.find(item => item[this.options.rowKey] === key);
        this.options.onExpand(rowData, false);
    }

    updateData(data) {
        this.options.data = data;
        this.render();
    }

    getSelectedRows() {
        return Array.from(this.selectedRows);
    }

    clearSelection() {
        this.selectedRows.clear();
        this.render();
    }
}

// 导出到全局
window.AccordionComponent = AccordionComponent;
window.SearchComponent = SearchComponent;
window.PaginationComponent = PaginationComponent;
window.TableComponent = TableComponent;
